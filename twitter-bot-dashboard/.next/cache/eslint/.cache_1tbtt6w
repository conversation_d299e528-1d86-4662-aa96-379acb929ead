[{"/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/generate/route.ts": "1", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/post/route.ts": "2", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/queue/[id]/route.ts": "3", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/queue/route.ts": "4", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/refresh/route.ts": "5", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/migrate/route.ts": "6", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/preferences/route.ts": "7", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/rss/feeds/[id]/route.ts": "8", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/rss/feeds/route.ts": "9", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/rss/items/route.ts": "10", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/scheduler/route.ts": "11", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/compose/page.tsx": "12", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/content/page.tsx": "13", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/feeds/page.tsx": "14", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/layout.tsx": "15", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/page.tsx": "16", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/components/TweetComposer.tsx": "17", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/components/TwitterStats.tsx": "18", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/api-config.ts": "19", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/openai.ts": "20", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/rss.ts": "21", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/scheduler.ts": "22", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/supabase.ts": "23", "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/scripts/migrate-existing-bot.ts": "24"}, {"size": 770, "mtime": 1751192507413, "results": "25", "hashOfConfig": "26"}, {"size": 3072, "mtime": 1751302674551, "results": "27", "hashOfConfig": "26"}, {"size": 611, "mtime": 1751193151672, "results": "28", "hashOfConfig": "26"}, {"size": 1582, "mtime": 1751233920243, "results": "29", "hashOfConfig": "26"}, {"size": 2098, "mtime": 1751193456179, "results": "30", "hashOfConfig": "26"}, {"size": 548, "mtime": 1751193467743, "results": "31", "hashOfConfig": "26"}, {"size": 1146, "mtime": 1751193250363, "results": "32", "hashOfConfig": "26"}, {"size": 1125, "mtime": 1751193168874, "results": "33", "hashOfConfig": "26"}, {"size": 960, "mtime": 1751193260584, "results": "34", "hashOfConfig": "26"}, {"size": 1696, "mtime": 1751193271361, "results": "35", "hashOfConfig": "26"}, {"size": 1389, "mtime": 1751193281529, "results": "36", "hashOfConfig": "26"}, {"size": 772, "mtime": 1751300833232, "results": "37", "hashOfConfig": "26"}, {"size": 12056, "mtime": 1751193334543, "results": "38", "hashOfConfig": "26"}, {"size": 23127, "mtime": 1751233889833, "results": "39", "hashOfConfig": "26"}, {"size": 689, "mtime": 1751190993312, "results": "40", "hashOfConfig": "26"}, {"size": 6109, "mtime": 1751300809878, "results": "41", "hashOfConfig": "26"}, {"size": 5232, "mtime": 1751300752460, "results": "42", "hashOfConfig": "26"}, {"size": 5626, "mtime": 1751300788081, "results": "43", "hashOfConfig": "26"}, {"size": 900, "mtime": 1751294729884, "results": "44", "hashOfConfig": "26"}, {"size": 6643, "mtime": 1751193370400, "results": "45", "hashOfConfig": "26"}, {"size": 5298, "mtime": 1751193525736, "results": "46", "hashOfConfig": "26"}, {"size": 6937, "mtime": 1751302766875, "results": "47", "hashOfConfig": "26"}, {"size": 8457, "mtime": 1751302867261, "results": "48", "hashOfConfig": "26"}, {"size": 3069, "mtime": 1751198998588, "results": "49", "hashOfConfig": "26"}, {"filePath": "50", "messages": "51", "suppressedMessages": "52", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "15v4ex5", {"filePath": "53", "messages": "54", "suppressedMessages": "55", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "56", "messages": "57", "suppressedMessages": "58", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "59", "messages": "60", "suppressedMessages": "61", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "62", "messages": "63", "suppressedMessages": "64", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "65", "messages": "66", "suppressedMessages": "67", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "68", "messages": "69", "suppressedMessages": "70", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "71", "messages": "72", "suppressedMessages": "73", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "74", "messages": "75", "suppressedMessages": "76", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "77", "messages": "78", "suppressedMessages": "79", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "80", "messages": "81", "suppressedMessages": "82", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "83", "messages": "84", "suppressedMessages": "85", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "86", "messages": "87", "suppressedMessages": "88", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "89", "messages": "90", "suppressedMessages": "91", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "92", "messages": "93", "suppressedMessages": "94", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "95", "messages": "96", "suppressedMessages": "97", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "98", "messages": "99", "suppressedMessages": "100", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "101", "messages": "102", "suppressedMessages": "103", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "104", "messages": "105", "suppressedMessages": "106", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "107", "messages": "108", "suppressedMessages": "109", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "110", "messages": "111", "suppressedMessages": "112", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "113", "messages": "114", "suppressedMessages": "115", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "116", "messages": "117", "suppressedMessages": "118", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "119", "messages": "120", "suppressedMessages": "121", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/generate/route.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/post/route.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/queue/[id]/route.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/queue/route.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/content/refresh/route.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/migrate/route.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/preferences/route.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/rss/feeds/[id]/route.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/rss/feeds/route.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/rss/items/route.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/api/scheduler/route.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/compose/page.tsx", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/content/page.tsx", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/feeds/page.tsx", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/layout.tsx", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/app/page.tsx", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/components/TweetComposer.tsx", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/components/TwitterStats.tsx", ["122"], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/api-config.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/openai.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/rss.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/scheduler.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/lib/supabase.ts", [], [], "/Users/<USER>/projects/AgentDevelopment/twitterbot/twitter-bot-dashboard/src/scripts/migrate-existing-bot.ts", [], [], {"ruleId": "123", "severity": 1, "message": "124", "line": 118, "column": 11, "nodeType": "125", "endLine": 122, "endColumn": 13}, "@next/next/no-img-element", "Using `<img>` could result in slower LCP and higher bandwidth. Consider using `<Image />` from `next/image` or a custom image loader to automatically optimize images. This may incur additional usage or cost from your provider. See: https://nextjs.org/docs/messages/no-img-element", "JSXOpeningElement"]