"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/domhandler";
exports.ids = ["vendor-chunks/domhandler"];
exports.modules = {

/***/ "(rsc)/./node_modules/domhandler/lib/esm/index.js":
/*!**************************************************!*\
  !*** ./node_modules/domhandler/lib/esm/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CDATA: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.CDATA),\n/* harmony export */   Comment: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.Comment),\n/* harmony export */   DataNode: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.DataNode),\n/* harmony export */   Document: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.Document),\n/* harmony export */   DomHandler: () => (/* binding */ DomHandler),\n/* harmony export */   Element: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.Element),\n/* harmony export */   Node: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.Node),\n/* harmony export */   NodeWithChildren: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.NodeWithChildren),\n/* harmony export */   ProcessingInstruction: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.ProcessingInstruction),\n/* harmony export */   Text: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.Text),\n/* harmony export */   cloneNode: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.cloneNode),\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   hasChildren: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.hasChildren),\n/* harmony export */   isCDATA: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.isCDATA),\n/* harmony export */   isComment: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.isComment),\n/* harmony export */   isDirective: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.isDirective),\n/* harmony export */   isDocument: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.isDocument),\n/* harmony export */   isTag: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.isTag),\n/* harmony export */   isText: () => (/* reexport safe */ _node_js__WEBPACK_IMPORTED_MODULE_1__.isText)\n/* harmony export */ });\n/* harmony import */ var domelementtype__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domelementtype */ \"(rsc)/./node_modules/domelementtype/lib/esm/index.js\");\n/* harmony import */ var _node_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./node.js */ \"(rsc)/./node_modules/domhandler/lib/esm/node.js\");\n\n\n\n// Default options\nconst defaultOpts = {\n    withStartIndices: false,\n    withEndIndices: false,\n    xmlMode: false,\n};\nclass DomHandler {\n    /**\n     * @param callback Called once parsing has completed.\n     * @param options Settings for the handler.\n     * @param elementCB Callback whenever a tag is closed.\n     */\n    constructor(callback, options, elementCB) {\n        /** The elements of the DOM */\n        this.dom = [];\n        /** The root element for the DOM */\n        this.root = new _node_js__WEBPACK_IMPORTED_MODULE_1__.Document(this.dom);\n        /** Indicated whether parsing has been completed. */\n        this.done = false;\n        /** Stack of open tags. */\n        this.tagStack = [this.root];\n        /** A data node that is still being written to. */\n        this.lastNode = null;\n        /** Reference to the parser instance. Used for location information. */\n        this.parser = null;\n        // Make it possible to skip arguments, for backwards-compatibility\n        if (typeof options === \"function\") {\n            elementCB = options;\n            options = defaultOpts;\n        }\n        if (typeof callback === \"object\") {\n            options = callback;\n            callback = undefined;\n        }\n        this.callback = callback !== null && callback !== void 0 ? callback : null;\n        this.options = options !== null && options !== void 0 ? options : defaultOpts;\n        this.elementCB = elementCB !== null && elementCB !== void 0 ? elementCB : null;\n    }\n    onparserinit(parser) {\n        this.parser = parser;\n    }\n    // Resets the handler back to starting state\n    onreset() {\n        this.dom = [];\n        this.root = new _node_js__WEBPACK_IMPORTED_MODULE_1__.Document(this.dom);\n        this.done = false;\n        this.tagStack = [this.root];\n        this.lastNode = null;\n        this.parser = null;\n    }\n    // Signals the handler that parsing is done\n    onend() {\n        if (this.done)\n            return;\n        this.done = true;\n        this.parser = null;\n        this.handleCallback(null);\n    }\n    onerror(error) {\n        this.handleCallback(error);\n    }\n    onclosetag() {\n        this.lastNode = null;\n        const elem = this.tagStack.pop();\n        if (this.options.withEndIndices) {\n            elem.endIndex = this.parser.endIndex;\n        }\n        if (this.elementCB)\n            this.elementCB(elem);\n    }\n    onopentag(name, attribs) {\n        const type = this.options.xmlMode ? domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Tag : undefined;\n        const element = new _node_js__WEBPACK_IMPORTED_MODULE_1__.Element(name, attribs, undefined, type);\n        this.addNode(element);\n        this.tagStack.push(element);\n    }\n    ontext(data) {\n        const { lastNode } = this;\n        if (lastNode && lastNode.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Text) {\n            lastNode.data += data;\n            if (this.options.withEndIndices) {\n                lastNode.endIndex = this.parser.endIndex;\n            }\n        }\n        else {\n            const node = new _node_js__WEBPACK_IMPORTED_MODULE_1__.Text(data);\n            this.addNode(node);\n            this.lastNode = node;\n        }\n    }\n    oncomment(data) {\n        if (this.lastNode && this.lastNode.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Comment) {\n            this.lastNode.data += data;\n            return;\n        }\n        const node = new _node_js__WEBPACK_IMPORTED_MODULE_1__.Comment(data);\n        this.addNode(node);\n        this.lastNode = node;\n    }\n    oncommentend() {\n        this.lastNode = null;\n    }\n    oncdatastart() {\n        const text = new _node_js__WEBPACK_IMPORTED_MODULE_1__.Text(\"\");\n        const node = new _node_js__WEBPACK_IMPORTED_MODULE_1__.CDATA([text]);\n        this.addNode(node);\n        text.parent = node;\n        this.lastNode = text;\n    }\n    oncdataend() {\n        this.lastNode = null;\n    }\n    onprocessinginstruction(name, data) {\n        const node = new _node_js__WEBPACK_IMPORTED_MODULE_1__.ProcessingInstruction(name, data);\n        this.addNode(node);\n    }\n    handleCallback(error) {\n        if (typeof this.callback === \"function\") {\n            this.callback(error, this.dom);\n        }\n        else if (error) {\n            throw error;\n        }\n    }\n    addNode(node) {\n        const parent = this.tagStack[this.tagStack.length - 1];\n        const previousSibling = parent.children[parent.children.length - 1];\n        if (this.options.withStartIndices) {\n            node.startIndex = this.parser.startIndex;\n        }\n        if (this.options.withEndIndices) {\n            node.endIndex = this.parser.endIndex;\n        }\n        parent.children.push(node);\n        if (previousSibling) {\n            node.prev = previousSibling;\n            previousSibling.next = node;\n        }\n        node.parent = parent;\n        this.lastNode = null;\n    }\n}\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (DomHandler);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domhandler/lib/esm/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domhandler/lib/esm/node.js":
/*!*************************************************!*\
  !*** ./node_modules/domhandler/lib/esm/node.js ***!
  \*************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CDATA: () => (/* binding */ CDATA),\n/* harmony export */   Comment: () => (/* binding */ Comment),\n/* harmony export */   DataNode: () => (/* binding */ DataNode),\n/* harmony export */   Document: () => (/* binding */ Document),\n/* harmony export */   Element: () => (/* binding */ Element),\n/* harmony export */   Node: () => (/* binding */ Node),\n/* harmony export */   NodeWithChildren: () => (/* binding */ NodeWithChildren),\n/* harmony export */   ProcessingInstruction: () => (/* binding */ ProcessingInstruction),\n/* harmony export */   Text: () => (/* binding */ Text),\n/* harmony export */   cloneNode: () => (/* binding */ cloneNode),\n/* harmony export */   hasChildren: () => (/* binding */ hasChildren),\n/* harmony export */   isCDATA: () => (/* binding */ isCDATA),\n/* harmony export */   isComment: () => (/* binding */ isComment),\n/* harmony export */   isDirective: () => (/* binding */ isDirective),\n/* harmony export */   isDocument: () => (/* binding */ isDocument),\n/* harmony export */   isTag: () => (/* binding */ isTag),\n/* harmony export */   isText: () => (/* binding */ isText)\n/* harmony export */ });\n/* harmony import */ var domelementtype__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! domelementtype */ \"(rsc)/./node_modules/domelementtype/lib/esm/index.js\");\n\n/**\n * This object will be used as the prototype for Nodes when creating a\n * DOM-Level-1-compliant structure.\n */\nclass Node {\n    constructor() {\n        /** Parent of the node */\n        this.parent = null;\n        /** Previous sibling */\n        this.prev = null;\n        /** Next sibling */\n        this.next = null;\n        /** The start index of the node. Requires `withStartIndices` on the handler to be `true. */\n        this.startIndex = null;\n        /** The end index of the node. Requires `withEndIndices` on the handler to be `true. */\n        this.endIndex = null;\n    }\n    // Read-write aliases for properties\n    /**\n     * Same as {@link parent}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get parentNode() {\n        return this.parent;\n    }\n    set parentNode(parent) {\n        this.parent = parent;\n    }\n    /**\n     * Same as {@link prev}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get previousSibling() {\n        return this.prev;\n    }\n    set previousSibling(prev) {\n        this.prev = prev;\n    }\n    /**\n     * Same as {@link next}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get nextSibling() {\n        return this.next;\n    }\n    set nextSibling(next) {\n        this.next = next;\n    }\n    /**\n     * Clone this node, and optionally its children.\n     *\n     * @param recursive Clone child nodes as well.\n     * @returns A clone of the node.\n     */\n    cloneNode(recursive = false) {\n        return cloneNode(this, recursive);\n    }\n}\n/**\n * A node that contains some data.\n */\nclass DataNode extends Node {\n    /**\n     * @param data The content of the data node\n     */\n    constructor(data) {\n        super();\n        this.data = data;\n    }\n    /**\n     * Same as {@link data}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get nodeValue() {\n        return this.data;\n    }\n    set nodeValue(data) {\n        this.data = data;\n    }\n}\n/**\n * Text within the document.\n */\nclass Text extends DataNode {\n    constructor() {\n        super(...arguments);\n        this.type = domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Text;\n    }\n    get nodeType() {\n        return 3;\n    }\n}\n/**\n * Comments within the document.\n */\nclass Comment extends DataNode {\n    constructor() {\n        super(...arguments);\n        this.type = domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Comment;\n    }\n    get nodeType() {\n        return 8;\n    }\n}\n/**\n * Processing instructions, including doc types.\n */\nclass ProcessingInstruction extends DataNode {\n    constructor(name, data) {\n        super(data);\n        this.name = name;\n        this.type = domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Directive;\n    }\n    get nodeType() {\n        return 1;\n    }\n}\n/**\n * A `Node` that can have children.\n */\nclass NodeWithChildren extends Node {\n    /**\n     * @param children Children of the node. Only certain node types can have children.\n     */\n    constructor(children) {\n        super();\n        this.children = children;\n    }\n    // Aliases\n    /** First child of the node. */\n    get firstChild() {\n        var _a;\n        return (_a = this.children[0]) !== null && _a !== void 0 ? _a : null;\n    }\n    /** Last child of the node. */\n    get lastChild() {\n        return this.children.length > 0\n            ? this.children[this.children.length - 1]\n            : null;\n    }\n    /**\n     * Same as {@link children}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get childNodes() {\n        return this.children;\n    }\n    set childNodes(children) {\n        this.children = children;\n    }\n}\nclass CDATA extends NodeWithChildren {\n    constructor() {\n        super(...arguments);\n        this.type = domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.CDATA;\n    }\n    get nodeType() {\n        return 4;\n    }\n}\n/**\n * The root node of the document.\n */\nclass Document extends NodeWithChildren {\n    constructor() {\n        super(...arguments);\n        this.type = domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Root;\n    }\n    get nodeType() {\n        return 9;\n    }\n}\n/**\n * An element within the DOM.\n */\nclass Element extends NodeWithChildren {\n    /**\n     * @param name Name of the tag, eg. `div`, `span`.\n     * @param attribs Object mapping attribute names to attribute values.\n     * @param children Children of the node.\n     */\n    constructor(name, attribs, children = [], type = name === \"script\"\n        ? domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Script\n        : name === \"style\"\n            ? domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Style\n            : domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Tag) {\n        super(children);\n        this.name = name;\n        this.attribs = attribs;\n        this.type = type;\n    }\n    get nodeType() {\n        return 1;\n    }\n    // DOM Level 1 aliases\n    /**\n     * Same as {@link name}.\n     * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n     */\n    get tagName() {\n        return this.name;\n    }\n    set tagName(name) {\n        this.name = name;\n    }\n    get attributes() {\n        return Object.keys(this.attribs).map((name) => {\n            var _a, _b;\n            return ({\n                name,\n                value: this.attribs[name],\n                namespace: (_a = this[\"x-attribsNamespace\"]) === null || _a === void 0 ? void 0 : _a[name],\n                prefix: (_b = this[\"x-attribsPrefix\"]) === null || _b === void 0 ? void 0 : _b[name],\n            });\n        });\n    }\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node is a `Element`, `false` otherwise.\n */\nfunction isTag(node) {\n    return (0,domelementtype__WEBPACK_IMPORTED_MODULE_0__.isTag)(node);\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `CDATA`, `false` otherwise.\n */\nfunction isCDATA(node) {\n    return node.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.CDATA;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `Text`, `false` otherwise.\n */\nfunction isText(node) {\n    return node.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Text;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `Comment`, `false` otherwise.\n */\nfunction isComment(node) {\n    return node.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Comment;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `ProcessingInstruction`, `false` otherwise.\n */\nfunction isDirective(node) {\n    return node.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Directive;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `ProcessingInstruction`, `false` otherwise.\n */\nfunction isDocument(node) {\n    return node.type === domelementtype__WEBPACK_IMPORTED_MODULE_0__.ElementType.Root;\n}\n/**\n * @param node Node to check.\n * @returns `true` if the node has children, `false` otherwise.\n */\nfunction hasChildren(node) {\n    return Object.prototype.hasOwnProperty.call(node, \"children\");\n}\n/**\n * Clone a node, and optionally its children.\n *\n * @param recursive Clone child nodes as well.\n * @returns A clone of the node.\n */\nfunction cloneNode(node, recursive = false) {\n    let result;\n    if (isText(node)) {\n        result = new Text(node.data);\n    }\n    else if (isComment(node)) {\n        result = new Comment(node.data);\n    }\n    else if (isTag(node)) {\n        const children = recursive ? cloneChildren(node.children) : [];\n        const clone = new Element(node.name, { ...node.attribs }, children);\n        children.forEach((child) => (child.parent = clone));\n        if (node.namespace != null) {\n            clone.namespace = node.namespace;\n        }\n        if (node[\"x-attribsNamespace\"]) {\n            clone[\"x-attribsNamespace\"] = { ...node[\"x-attribsNamespace\"] };\n        }\n        if (node[\"x-attribsPrefix\"]) {\n            clone[\"x-attribsPrefix\"] = { ...node[\"x-attribsPrefix\"] };\n        }\n        result = clone;\n    }\n    else if (isCDATA(node)) {\n        const children = recursive ? cloneChildren(node.children) : [];\n        const clone = new CDATA(children);\n        children.forEach((child) => (child.parent = clone));\n        result = clone;\n    }\n    else if (isDocument(node)) {\n        const children = recursive ? cloneChildren(node.children) : [];\n        const clone = new Document(children);\n        children.forEach((child) => (child.parent = clone));\n        if (node[\"x-mode\"]) {\n            clone[\"x-mode\"] = node[\"x-mode\"];\n        }\n        result = clone;\n    }\n    else if (isDirective(node)) {\n        const instruction = new ProcessingInstruction(node.name, node.data);\n        if (node[\"x-name\"] != null) {\n            instruction[\"x-name\"] = node[\"x-name\"];\n            instruction[\"x-publicId\"] = node[\"x-publicId\"];\n            instruction[\"x-systemId\"] = node[\"x-systemId\"];\n        }\n        result = instruction;\n    }\n    else {\n        throw new Error(`Not implemented yet: ${node.type}`);\n    }\n    result.startIndex = node.startIndex;\n    result.endIndex = node.endIndex;\n    if (node.sourceCodeLocation != null) {\n        result.sourceCodeLocation = node.sourceCodeLocation;\n    }\n    return result;\n}\nfunction cloneChildren(childs) {\n    const children = childs.map((child) => cloneNode(child, true));\n    for (let i = 1; i < children.length; i++) {\n        children[i].prev = children[i - 1];\n        children[i - 1].next = children[i];\n    }\n    return children;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domhandler/lib/esm/node.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domhandler/lib/index.js":
/*!**********************************************!*\
  !*** ./node_modules/domhandler/lib/index.js ***!
  \**********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    var desc = Object.getOwnPropertyDescriptor(m, k);\n    if (!desc || (\"get\" in desc ? !m.__esModule : desc.writable || desc.configurable)) {\n      desc = { enumerable: true, get: function() { return m[k]; } };\n    }\n    Object.defineProperty(o, k2, desc);\n}) : (function(o, m, k, k2) {\n    if (k2 === undefined) k2 = k;\n    o[k2] = m[k];\n}));\nvar __exportStar = (this && this.__exportStar) || function(m, exports) {\n    for (var p in m) if (p !== \"default\" && !Object.prototype.hasOwnProperty.call(exports, p)) __createBinding(exports, m, p);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.DomHandler = void 0;\nvar domelementtype_1 = __webpack_require__(/*! domelementtype */ \"(rsc)/./node_modules/domelementtype/lib/index.js\");\nvar node_js_1 = __webpack_require__(/*! ./node.js */ \"(rsc)/./node_modules/domhandler/lib/node.js\");\n__exportStar(__webpack_require__(/*! ./node.js */ \"(rsc)/./node_modules/domhandler/lib/node.js\"), exports);\n// Default options\nvar defaultOpts = {\n    withStartIndices: false,\n    withEndIndices: false,\n    xmlMode: false,\n};\nvar DomHandler = /** @class */ (function () {\n    /**\n     * @param callback Called once parsing has completed.\n     * @param options Settings for the handler.\n     * @param elementCB Callback whenever a tag is closed.\n     */\n    function DomHandler(callback, options, elementCB) {\n        /** The elements of the DOM */\n        this.dom = [];\n        /** The root element for the DOM */\n        this.root = new node_js_1.Document(this.dom);\n        /** Indicated whether parsing has been completed. */\n        this.done = false;\n        /** Stack of open tags. */\n        this.tagStack = [this.root];\n        /** A data node that is still being written to. */\n        this.lastNode = null;\n        /** Reference to the parser instance. Used for location information. */\n        this.parser = null;\n        // Make it possible to skip arguments, for backwards-compatibility\n        if (typeof options === \"function\") {\n            elementCB = options;\n            options = defaultOpts;\n        }\n        if (typeof callback === \"object\") {\n            options = callback;\n            callback = undefined;\n        }\n        this.callback = callback !== null && callback !== void 0 ? callback : null;\n        this.options = options !== null && options !== void 0 ? options : defaultOpts;\n        this.elementCB = elementCB !== null && elementCB !== void 0 ? elementCB : null;\n    }\n    DomHandler.prototype.onparserinit = function (parser) {\n        this.parser = parser;\n    };\n    // Resets the handler back to starting state\n    DomHandler.prototype.onreset = function () {\n        this.dom = [];\n        this.root = new node_js_1.Document(this.dom);\n        this.done = false;\n        this.tagStack = [this.root];\n        this.lastNode = null;\n        this.parser = null;\n    };\n    // Signals the handler that parsing is done\n    DomHandler.prototype.onend = function () {\n        if (this.done)\n            return;\n        this.done = true;\n        this.parser = null;\n        this.handleCallback(null);\n    };\n    DomHandler.prototype.onerror = function (error) {\n        this.handleCallback(error);\n    };\n    DomHandler.prototype.onclosetag = function () {\n        this.lastNode = null;\n        var elem = this.tagStack.pop();\n        if (this.options.withEndIndices) {\n            elem.endIndex = this.parser.endIndex;\n        }\n        if (this.elementCB)\n            this.elementCB(elem);\n    };\n    DomHandler.prototype.onopentag = function (name, attribs) {\n        var type = this.options.xmlMode ? domelementtype_1.ElementType.Tag : undefined;\n        var element = new node_js_1.Element(name, attribs, undefined, type);\n        this.addNode(element);\n        this.tagStack.push(element);\n    };\n    DomHandler.prototype.ontext = function (data) {\n        var lastNode = this.lastNode;\n        if (lastNode && lastNode.type === domelementtype_1.ElementType.Text) {\n            lastNode.data += data;\n            if (this.options.withEndIndices) {\n                lastNode.endIndex = this.parser.endIndex;\n            }\n        }\n        else {\n            var node = new node_js_1.Text(data);\n            this.addNode(node);\n            this.lastNode = node;\n        }\n    };\n    DomHandler.prototype.oncomment = function (data) {\n        if (this.lastNode && this.lastNode.type === domelementtype_1.ElementType.Comment) {\n            this.lastNode.data += data;\n            return;\n        }\n        var node = new node_js_1.Comment(data);\n        this.addNode(node);\n        this.lastNode = node;\n    };\n    DomHandler.prototype.oncommentend = function () {\n        this.lastNode = null;\n    };\n    DomHandler.prototype.oncdatastart = function () {\n        var text = new node_js_1.Text(\"\");\n        var node = new node_js_1.CDATA([text]);\n        this.addNode(node);\n        text.parent = node;\n        this.lastNode = text;\n    };\n    DomHandler.prototype.oncdataend = function () {\n        this.lastNode = null;\n    };\n    DomHandler.prototype.onprocessinginstruction = function (name, data) {\n        var node = new node_js_1.ProcessingInstruction(name, data);\n        this.addNode(node);\n    };\n    DomHandler.prototype.handleCallback = function (error) {\n        if (typeof this.callback === \"function\") {\n            this.callback(error, this.dom);\n        }\n        else if (error) {\n            throw error;\n        }\n    };\n    DomHandler.prototype.addNode = function (node) {\n        var parent = this.tagStack[this.tagStack.length - 1];\n        var previousSibling = parent.children[parent.children.length - 1];\n        if (this.options.withStartIndices) {\n            node.startIndex = this.parser.startIndex;\n        }\n        if (this.options.withEndIndices) {\n            node.endIndex = this.parser.endIndex;\n        }\n        parent.children.push(node);\n        if (previousSibling) {\n            node.prev = previousSibling;\n            previousSibling.next = node;\n        }\n        node.parent = parent;\n        this.lastNode = null;\n    };\n    return DomHandler;\n}());\nexports.DomHandler = DomHandler;\nexports[\"default\"] = DomHandler;\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvZG9taGFuZGxlci9saWIvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQWE7QUFDYjtBQUNBO0FBQ0E7QUFDQTtBQUNBLGVBQWUsb0NBQW9DO0FBQ25EO0FBQ0E7QUFDQSxDQUFDO0FBQ0Q7QUFDQTtBQUNBLENBQUM7QUFDRDtBQUNBO0FBQ0E7QUFDQSw4Q0FBNkMsRUFBRSxhQUFhLEVBQUM7QUFDN0Qsa0JBQWtCO0FBQ2xCLHVCQUF1QixtQkFBTyxDQUFDLHdFQUFnQjtBQUMvQyxnQkFBZ0IsbUJBQU8sQ0FBQyw4REFBVztBQUNuQyxhQUFhLG1CQUFPLENBQUMsOERBQVc7QUFDaEM7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSxDQUFDO0FBQ0Qsa0JBQWtCO0FBQ2xCLGtCQUFlIiwic291cmNlcyI6WyIvVXNlcnMvc2FudGhvc2hwYWxhbmlzYW15L3Byb2plY3RzL0FnZW50RGV2ZWxvcG1lbnQvdHdpdHRlcmJvdC90d2l0dGVyLWJvdC1kYXNoYm9hcmQvbm9kZV9tb2R1bGVzL2RvbWhhbmRsZXIvbGliL2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIHN0cmljdFwiO1xudmFyIF9fY3JlYXRlQmluZGluZyA9ICh0aGlzICYmIHRoaXMuX19jcmVhdGVCaW5kaW5nKSB8fCAoT2JqZWN0LmNyZWF0ZSA/IChmdW5jdGlvbihvLCBtLCBrLCBrMikge1xuICAgIGlmIChrMiA9PT0gdW5kZWZpbmVkKSBrMiA9IGs7XG4gICAgdmFyIGRlc2MgPSBPYmplY3QuZ2V0T3duUHJvcGVydHlEZXNjcmlwdG9yKG0sIGspO1xuICAgIGlmICghZGVzYyB8fCAoXCJnZXRcIiBpbiBkZXNjID8gIW0uX19lc01vZHVsZSA6IGRlc2Mud3JpdGFibGUgfHwgZGVzYy5jb25maWd1cmFibGUpKSB7XG4gICAgICBkZXNjID0geyBlbnVtZXJhYmxlOiB0cnVlLCBnZXQ6IGZ1bmN0aW9uKCkgeyByZXR1cm4gbVtrXTsgfSB9O1xuICAgIH1cbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkobywgazIsIGRlc2MpO1xufSkgOiAoZnVuY3Rpb24obywgbSwgaywgazIpIHtcbiAgICBpZiAoazIgPT09IHVuZGVmaW5lZCkgazIgPSBrO1xuICAgIG9bazJdID0gbVtrXTtcbn0pKTtcbnZhciBfX2V4cG9ydFN0YXIgPSAodGhpcyAmJiB0aGlzLl9fZXhwb3J0U3RhcikgfHwgZnVuY3Rpb24obSwgZXhwb3J0cykge1xuICAgIGZvciAodmFyIHAgaW4gbSkgaWYgKHAgIT09IFwiZGVmYXVsdFwiICYmICFPYmplY3QucHJvdG90eXBlLmhhc093blByb3BlcnR5LmNhbGwoZXhwb3J0cywgcCkpIF9fY3JlYXRlQmluZGluZyhleHBvcnRzLCBtLCBwKTtcbn07XG5PYmplY3QuZGVmaW5lUHJvcGVydHkoZXhwb3J0cywgXCJfX2VzTW9kdWxlXCIsIHsgdmFsdWU6IHRydWUgfSk7XG5leHBvcnRzLkRvbUhhbmRsZXIgPSB2b2lkIDA7XG52YXIgZG9tZWxlbWVudHR5cGVfMSA9IHJlcXVpcmUoXCJkb21lbGVtZW50dHlwZVwiKTtcbnZhciBub2RlX2pzXzEgPSByZXF1aXJlKFwiLi9ub2RlLmpzXCIpO1xuX19leHBvcnRTdGFyKHJlcXVpcmUoXCIuL25vZGUuanNcIiksIGV4cG9ydHMpO1xuLy8gRGVmYXVsdCBvcHRpb25zXG52YXIgZGVmYXVsdE9wdHMgPSB7XG4gICAgd2l0aFN0YXJ0SW5kaWNlczogZmFsc2UsXG4gICAgd2l0aEVuZEluZGljZXM6IGZhbHNlLFxuICAgIHhtbE1vZGU6IGZhbHNlLFxufTtcbnZhciBEb21IYW5kbGVyID0gLyoqIEBjbGFzcyAqLyAoZnVuY3Rpb24gKCkge1xuICAgIC8qKlxuICAgICAqIEBwYXJhbSBjYWxsYmFjayBDYWxsZWQgb25jZSBwYXJzaW5nIGhhcyBjb21wbGV0ZWQuXG4gICAgICogQHBhcmFtIG9wdGlvbnMgU2V0dGluZ3MgZm9yIHRoZSBoYW5kbGVyLlxuICAgICAqIEBwYXJhbSBlbGVtZW50Q0IgQ2FsbGJhY2sgd2hlbmV2ZXIgYSB0YWcgaXMgY2xvc2VkLlxuICAgICAqL1xuICAgIGZ1bmN0aW9uIERvbUhhbmRsZXIoY2FsbGJhY2ssIG9wdGlvbnMsIGVsZW1lbnRDQikge1xuICAgICAgICAvKiogVGhlIGVsZW1lbnRzIG9mIHRoZSBET00gKi9cbiAgICAgICAgdGhpcy5kb20gPSBbXTtcbiAgICAgICAgLyoqIFRoZSByb290IGVsZW1lbnQgZm9yIHRoZSBET00gKi9cbiAgICAgICAgdGhpcy5yb290ID0gbmV3IG5vZGVfanNfMS5Eb2N1bWVudCh0aGlzLmRvbSk7XG4gICAgICAgIC8qKiBJbmRpY2F0ZWQgd2hldGhlciBwYXJzaW5nIGhhcyBiZWVuIGNvbXBsZXRlZC4gKi9cbiAgICAgICAgdGhpcy5kb25lID0gZmFsc2U7XG4gICAgICAgIC8qKiBTdGFjayBvZiBvcGVuIHRhZ3MuICovXG4gICAgICAgIHRoaXMudGFnU3RhY2sgPSBbdGhpcy5yb290XTtcbiAgICAgICAgLyoqIEEgZGF0YSBub2RlIHRoYXQgaXMgc3RpbGwgYmVpbmcgd3JpdHRlbiB0by4gKi9cbiAgICAgICAgdGhpcy5sYXN0Tm9kZSA9IG51bGw7XG4gICAgICAgIC8qKiBSZWZlcmVuY2UgdG8gdGhlIHBhcnNlciBpbnN0YW5jZS4gVXNlZCBmb3IgbG9jYXRpb24gaW5mb3JtYXRpb24uICovXG4gICAgICAgIHRoaXMucGFyc2VyID0gbnVsbDtcbiAgICAgICAgLy8gTWFrZSBpdCBwb3NzaWJsZSB0byBza2lwIGFyZ3VtZW50cywgZm9yIGJhY2t3YXJkcy1jb21wYXRpYmlsaXR5XG4gICAgICAgIGlmICh0eXBlb2Ygb3B0aW9ucyA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgICAgICAgICBlbGVtZW50Q0IgPSBvcHRpb25zO1xuICAgICAgICAgICAgb3B0aW9ucyA9IGRlZmF1bHRPcHRzO1xuICAgICAgICB9XG4gICAgICAgIGlmICh0eXBlb2YgY2FsbGJhY2sgPT09IFwib2JqZWN0XCIpIHtcbiAgICAgICAgICAgIG9wdGlvbnMgPSBjYWxsYmFjaztcbiAgICAgICAgICAgIGNhbGxiYWNrID0gdW5kZWZpbmVkO1xuICAgICAgICB9XG4gICAgICAgIHRoaXMuY2FsbGJhY2sgPSBjYWxsYmFjayAhPT0gbnVsbCAmJiBjYWxsYmFjayAhPT0gdm9pZCAwID8gY2FsbGJhY2sgOiBudWxsO1xuICAgICAgICB0aGlzLm9wdGlvbnMgPSBvcHRpb25zICE9PSBudWxsICYmIG9wdGlvbnMgIT09IHZvaWQgMCA/IG9wdGlvbnMgOiBkZWZhdWx0T3B0cztcbiAgICAgICAgdGhpcy5lbGVtZW50Q0IgPSBlbGVtZW50Q0IgIT09IG51bGwgJiYgZWxlbWVudENCICE9PSB2b2lkIDAgPyBlbGVtZW50Q0IgOiBudWxsO1xuICAgIH1cbiAgICBEb21IYW5kbGVyLnByb3RvdHlwZS5vbnBhcnNlcmluaXQgPSBmdW5jdGlvbiAocGFyc2VyKSB7XG4gICAgICAgIHRoaXMucGFyc2VyID0gcGFyc2VyO1xuICAgIH07XG4gICAgLy8gUmVzZXRzIHRoZSBoYW5kbGVyIGJhY2sgdG8gc3RhcnRpbmcgc3RhdGVcbiAgICBEb21IYW5kbGVyLnByb3RvdHlwZS5vbnJlc2V0ID0gZnVuY3Rpb24gKCkge1xuICAgICAgICB0aGlzLmRvbSA9IFtdO1xuICAgICAgICB0aGlzLnJvb3QgPSBuZXcgbm9kZV9qc18xLkRvY3VtZW50KHRoaXMuZG9tKTtcbiAgICAgICAgdGhpcy5kb25lID0gZmFsc2U7XG4gICAgICAgIHRoaXMudGFnU3RhY2sgPSBbdGhpcy5yb290XTtcbiAgICAgICAgdGhpcy5sYXN0Tm9kZSA9IG51bGw7XG4gICAgICAgIHRoaXMucGFyc2VyID0gbnVsbDtcbiAgICB9O1xuICAgIC8vIFNpZ25hbHMgdGhlIGhhbmRsZXIgdGhhdCBwYXJzaW5nIGlzIGRvbmVcbiAgICBEb21IYW5kbGVyLnByb3RvdHlwZS5vbmVuZCA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgaWYgKHRoaXMuZG9uZSlcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgdGhpcy5kb25lID0gdHJ1ZTtcbiAgICAgICAgdGhpcy5wYXJzZXIgPSBudWxsO1xuICAgICAgICB0aGlzLmhhbmRsZUNhbGxiYWNrKG51bGwpO1xuICAgIH07XG4gICAgRG9tSGFuZGxlci5wcm90b3R5cGUub25lcnJvciA9IGZ1bmN0aW9uIChlcnJvcikge1xuICAgICAgICB0aGlzLmhhbmRsZUNhbGxiYWNrKGVycm9yKTtcbiAgICB9O1xuICAgIERvbUhhbmRsZXIucHJvdG90eXBlLm9uY2xvc2V0YWcgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHRoaXMubGFzdE5vZGUgPSBudWxsO1xuICAgICAgICB2YXIgZWxlbSA9IHRoaXMudGFnU3RhY2sucG9wKCk7XG4gICAgICAgIGlmICh0aGlzLm9wdGlvbnMud2l0aEVuZEluZGljZXMpIHtcbiAgICAgICAgICAgIGVsZW0uZW5kSW5kZXggPSB0aGlzLnBhcnNlci5lbmRJbmRleDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodGhpcy5lbGVtZW50Q0IpXG4gICAgICAgICAgICB0aGlzLmVsZW1lbnRDQihlbGVtKTtcbiAgICB9O1xuICAgIERvbUhhbmRsZXIucHJvdG90eXBlLm9ub3BlbnRhZyA9IGZ1bmN0aW9uIChuYW1lLCBhdHRyaWJzKSB7XG4gICAgICAgIHZhciB0eXBlID0gdGhpcy5vcHRpb25zLnhtbE1vZGUgPyBkb21lbGVtZW50dHlwZV8xLkVsZW1lbnRUeXBlLlRhZyA6IHVuZGVmaW5lZDtcbiAgICAgICAgdmFyIGVsZW1lbnQgPSBuZXcgbm9kZV9qc18xLkVsZW1lbnQobmFtZSwgYXR0cmlicywgdW5kZWZpbmVkLCB0eXBlKTtcbiAgICAgICAgdGhpcy5hZGROb2RlKGVsZW1lbnQpO1xuICAgICAgICB0aGlzLnRhZ1N0YWNrLnB1c2goZWxlbWVudCk7XG4gICAgfTtcbiAgICBEb21IYW5kbGVyLnByb3RvdHlwZS5vbnRleHQgPSBmdW5jdGlvbiAoZGF0YSkge1xuICAgICAgICB2YXIgbGFzdE5vZGUgPSB0aGlzLmxhc3ROb2RlO1xuICAgICAgICBpZiAobGFzdE5vZGUgJiYgbGFzdE5vZGUudHlwZSA9PT0gZG9tZWxlbWVudHR5cGVfMS5FbGVtZW50VHlwZS5UZXh0KSB7XG4gICAgICAgICAgICBsYXN0Tm9kZS5kYXRhICs9IGRhdGE7XG4gICAgICAgICAgICBpZiAodGhpcy5vcHRpb25zLndpdGhFbmRJbmRpY2VzKSB7XG4gICAgICAgICAgICAgICAgbGFzdE5vZGUuZW5kSW5kZXggPSB0aGlzLnBhcnNlci5lbmRJbmRleDtcbiAgICAgICAgICAgIH1cbiAgICAgICAgfVxuICAgICAgICBlbHNlIHtcbiAgICAgICAgICAgIHZhciBub2RlID0gbmV3IG5vZGVfanNfMS5UZXh0KGRhdGEpO1xuICAgICAgICAgICAgdGhpcy5hZGROb2RlKG5vZGUpO1xuICAgICAgICAgICAgdGhpcy5sYXN0Tm9kZSA9IG5vZGU7XG4gICAgICAgIH1cbiAgICB9O1xuICAgIERvbUhhbmRsZXIucHJvdG90eXBlLm9uY29tbWVudCA9IGZ1bmN0aW9uIChkYXRhKSB7XG4gICAgICAgIGlmICh0aGlzLmxhc3ROb2RlICYmIHRoaXMubGFzdE5vZGUudHlwZSA9PT0gZG9tZWxlbWVudHR5cGVfMS5FbGVtZW50VHlwZS5Db21tZW50KSB7XG4gICAgICAgICAgICB0aGlzLmxhc3ROb2RlLmRhdGEgKz0gZGF0YTtcbiAgICAgICAgICAgIHJldHVybjtcbiAgICAgICAgfVxuICAgICAgICB2YXIgbm9kZSA9IG5ldyBub2RlX2pzXzEuQ29tbWVudChkYXRhKTtcbiAgICAgICAgdGhpcy5hZGROb2RlKG5vZGUpO1xuICAgICAgICB0aGlzLmxhc3ROb2RlID0gbm9kZTtcbiAgICB9O1xuICAgIERvbUhhbmRsZXIucHJvdG90eXBlLm9uY29tbWVudGVuZCA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgdGhpcy5sYXN0Tm9kZSA9IG51bGw7XG4gICAgfTtcbiAgICBEb21IYW5kbGVyLnByb3RvdHlwZS5vbmNkYXRhc3RhcnQgPSBmdW5jdGlvbiAoKSB7XG4gICAgICAgIHZhciB0ZXh0ID0gbmV3IG5vZGVfanNfMS5UZXh0KFwiXCIpO1xuICAgICAgICB2YXIgbm9kZSA9IG5ldyBub2RlX2pzXzEuQ0RBVEEoW3RleHRdKTtcbiAgICAgICAgdGhpcy5hZGROb2RlKG5vZGUpO1xuICAgICAgICB0ZXh0LnBhcmVudCA9IG5vZGU7XG4gICAgICAgIHRoaXMubGFzdE5vZGUgPSB0ZXh0O1xuICAgIH07XG4gICAgRG9tSGFuZGxlci5wcm90b3R5cGUub25jZGF0YWVuZCA9IGZ1bmN0aW9uICgpIHtcbiAgICAgICAgdGhpcy5sYXN0Tm9kZSA9IG51bGw7XG4gICAgfTtcbiAgICBEb21IYW5kbGVyLnByb3RvdHlwZS5vbnByb2Nlc3NpbmdpbnN0cnVjdGlvbiA9IGZ1bmN0aW9uIChuYW1lLCBkYXRhKSB7XG4gICAgICAgIHZhciBub2RlID0gbmV3IG5vZGVfanNfMS5Qcm9jZXNzaW5nSW5zdHJ1Y3Rpb24obmFtZSwgZGF0YSk7XG4gICAgICAgIHRoaXMuYWRkTm9kZShub2RlKTtcbiAgICB9O1xuICAgIERvbUhhbmRsZXIucHJvdG90eXBlLmhhbmRsZUNhbGxiYWNrID0gZnVuY3Rpb24gKGVycm9yKSB7XG4gICAgICAgIGlmICh0eXBlb2YgdGhpcy5jYWxsYmFjayA9PT0gXCJmdW5jdGlvblwiKSB7XG4gICAgICAgICAgICB0aGlzLmNhbGxiYWNrKGVycm9yLCB0aGlzLmRvbSk7XG4gICAgICAgIH1cbiAgICAgICAgZWxzZSBpZiAoZXJyb3IpIHtcbiAgICAgICAgICAgIHRocm93IGVycm9yO1xuICAgICAgICB9XG4gICAgfTtcbiAgICBEb21IYW5kbGVyLnByb3RvdHlwZS5hZGROb2RlID0gZnVuY3Rpb24gKG5vZGUpIHtcbiAgICAgICAgdmFyIHBhcmVudCA9IHRoaXMudGFnU3RhY2tbdGhpcy50YWdTdGFjay5sZW5ndGggLSAxXTtcbiAgICAgICAgdmFyIHByZXZpb3VzU2libGluZyA9IHBhcmVudC5jaGlsZHJlbltwYXJlbnQuY2hpbGRyZW4ubGVuZ3RoIC0gMV07XG4gICAgICAgIGlmICh0aGlzLm9wdGlvbnMud2l0aFN0YXJ0SW5kaWNlcykge1xuICAgICAgICAgICAgbm9kZS5zdGFydEluZGV4ID0gdGhpcy5wYXJzZXIuc3RhcnRJbmRleDtcbiAgICAgICAgfVxuICAgICAgICBpZiAodGhpcy5vcHRpb25zLndpdGhFbmRJbmRpY2VzKSB7XG4gICAgICAgICAgICBub2RlLmVuZEluZGV4ID0gdGhpcy5wYXJzZXIuZW5kSW5kZXg7XG4gICAgICAgIH1cbiAgICAgICAgcGFyZW50LmNoaWxkcmVuLnB1c2gobm9kZSk7XG4gICAgICAgIGlmIChwcmV2aW91c1NpYmxpbmcpIHtcbiAgICAgICAgICAgIG5vZGUucHJldiA9IHByZXZpb3VzU2libGluZztcbiAgICAgICAgICAgIHByZXZpb3VzU2libGluZy5uZXh0ID0gbm9kZTtcbiAgICAgICAgfVxuICAgICAgICBub2RlLnBhcmVudCA9IHBhcmVudDtcbiAgICAgICAgdGhpcy5sYXN0Tm9kZSA9IG51bGw7XG4gICAgfTtcbiAgICByZXR1cm4gRG9tSGFuZGxlcjtcbn0oKSk7XG5leHBvcnRzLkRvbUhhbmRsZXIgPSBEb21IYW5kbGVyO1xuZXhwb3J0cy5kZWZhdWx0ID0gRG9tSGFuZGxlcjtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domhandler/lib/index.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/domhandler/lib/node.js":
/*!*********************************************!*\
  !*** ./node_modules/domhandler/lib/node.js ***!
  \*********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("\nvar __extends = (this && this.__extends) || (function () {\n    var extendStatics = function (d, b) {\n        extendStatics = Object.setPrototypeOf ||\n            ({ __proto__: [] } instanceof Array && function (d, b) { d.__proto__ = b; }) ||\n            function (d, b) { for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p]; };\n        return extendStatics(d, b);\n    };\n    return function (d, b) {\n        if (typeof b !== \"function\" && b !== null)\n            throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n        extendStatics(d, b);\n        function __() { this.constructor = d; }\n        d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n    };\n})();\nvar __assign = (this && this.__assign) || function () {\n    __assign = Object.assign || function(t) {\n        for (var s, i = 1, n = arguments.length; i < n; i++) {\n            s = arguments[i];\n            for (var p in s) if (Object.prototype.hasOwnProperty.call(s, p))\n                t[p] = s[p];\n        }\n        return t;\n    };\n    return __assign.apply(this, arguments);\n};\nObject.defineProperty(exports, \"__esModule\", ({ value: true }));\nexports.cloneNode = exports.hasChildren = exports.isDocument = exports.isDirective = exports.isComment = exports.isText = exports.isCDATA = exports.isTag = exports.Element = exports.Document = exports.CDATA = exports.NodeWithChildren = exports.ProcessingInstruction = exports.Comment = exports.Text = exports.DataNode = exports.Node = void 0;\nvar domelementtype_1 = __webpack_require__(/*! domelementtype */ \"(rsc)/./node_modules/domelementtype/lib/index.js\");\n/**\n * This object will be used as the prototype for Nodes when creating a\n * DOM-Level-1-compliant structure.\n */\nvar Node = /** @class */ (function () {\n    function Node() {\n        /** Parent of the node */\n        this.parent = null;\n        /** Previous sibling */\n        this.prev = null;\n        /** Next sibling */\n        this.next = null;\n        /** The start index of the node. Requires `withStartIndices` on the handler to be `true. */\n        this.startIndex = null;\n        /** The end index of the node. Requires `withEndIndices` on the handler to be `true. */\n        this.endIndex = null;\n    }\n    Object.defineProperty(Node.prototype, \"parentNode\", {\n        // Read-write aliases for properties\n        /**\n         * Same as {@link parent}.\n         * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n         */\n        get: function () {\n            return this.parent;\n        },\n        set: function (parent) {\n            this.parent = parent;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Node.prototype, \"previousSibling\", {\n        /**\n         * Same as {@link prev}.\n         * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n         */\n        get: function () {\n            return this.prev;\n        },\n        set: function (prev) {\n            this.prev = prev;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Node.prototype, \"nextSibling\", {\n        /**\n         * Same as {@link next}.\n         * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n         */\n        get: function () {\n            return this.next;\n        },\n        set: function (next) {\n            this.next = next;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    /**\n     * Clone this node, and optionally its children.\n     *\n     * @param recursive Clone child nodes as well.\n     * @returns A clone of the node.\n     */\n    Node.prototype.cloneNode = function (recursive) {\n        if (recursive === void 0) { recursive = false; }\n        return cloneNode(this, recursive);\n    };\n    return Node;\n}());\nexports.Node = Node;\n/**\n * A node that contains some data.\n */\nvar DataNode = /** @class */ (function (_super) {\n    __extends(DataNode, _super);\n    /**\n     * @param data The content of the data node\n     */\n    function DataNode(data) {\n        var _this = _super.call(this) || this;\n        _this.data = data;\n        return _this;\n    }\n    Object.defineProperty(DataNode.prototype, \"nodeValue\", {\n        /**\n         * Same as {@link data}.\n         * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n         */\n        get: function () {\n            return this.data;\n        },\n        set: function (data) {\n            this.data = data;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return DataNode;\n}(Node));\nexports.DataNode = DataNode;\n/**\n * Text within the document.\n */\nvar Text = /** @class */ (function (_super) {\n    __extends(Text, _super);\n    function Text() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.type = domelementtype_1.ElementType.Text;\n        return _this;\n    }\n    Object.defineProperty(Text.prototype, \"nodeType\", {\n        get: function () {\n            return 3;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return Text;\n}(DataNode));\nexports.Text = Text;\n/**\n * Comments within the document.\n */\nvar Comment = /** @class */ (function (_super) {\n    __extends(Comment, _super);\n    function Comment() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.type = domelementtype_1.ElementType.Comment;\n        return _this;\n    }\n    Object.defineProperty(Comment.prototype, \"nodeType\", {\n        get: function () {\n            return 8;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return Comment;\n}(DataNode));\nexports.Comment = Comment;\n/**\n * Processing instructions, including doc types.\n */\nvar ProcessingInstruction = /** @class */ (function (_super) {\n    __extends(ProcessingInstruction, _super);\n    function ProcessingInstruction(name, data) {\n        var _this = _super.call(this, data) || this;\n        _this.name = name;\n        _this.type = domelementtype_1.ElementType.Directive;\n        return _this;\n    }\n    Object.defineProperty(ProcessingInstruction.prototype, \"nodeType\", {\n        get: function () {\n            return 1;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return ProcessingInstruction;\n}(DataNode));\nexports.ProcessingInstruction = ProcessingInstruction;\n/**\n * A `Node` that can have children.\n */\nvar NodeWithChildren = /** @class */ (function (_super) {\n    __extends(NodeWithChildren, _super);\n    /**\n     * @param children Children of the node. Only certain node types can have children.\n     */\n    function NodeWithChildren(children) {\n        var _this = _super.call(this) || this;\n        _this.children = children;\n        return _this;\n    }\n    Object.defineProperty(NodeWithChildren.prototype, \"firstChild\", {\n        // Aliases\n        /** First child of the node. */\n        get: function () {\n            var _a;\n            return (_a = this.children[0]) !== null && _a !== void 0 ? _a : null;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(NodeWithChildren.prototype, \"lastChild\", {\n        /** Last child of the node. */\n        get: function () {\n            return this.children.length > 0\n                ? this.children[this.children.length - 1]\n                : null;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(NodeWithChildren.prototype, \"childNodes\", {\n        /**\n         * Same as {@link children}.\n         * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n         */\n        get: function () {\n            return this.children;\n        },\n        set: function (children) {\n            this.children = children;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return NodeWithChildren;\n}(Node));\nexports.NodeWithChildren = NodeWithChildren;\nvar CDATA = /** @class */ (function (_super) {\n    __extends(CDATA, _super);\n    function CDATA() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.type = domelementtype_1.ElementType.CDATA;\n        return _this;\n    }\n    Object.defineProperty(CDATA.prototype, \"nodeType\", {\n        get: function () {\n            return 4;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return CDATA;\n}(NodeWithChildren));\nexports.CDATA = CDATA;\n/**\n * The root node of the document.\n */\nvar Document = /** @class */ (function (_super) {\n    __extends(Document, _super);\n    function Document() {\n        var _this = _super !== null && _super.apply(this, arguments) || this;\n        _this.type = domelementtype_1.ElementType.Root;\n        return _this;\n    }\n    Object.defineProperty(Document.prototype, \"nodeType\", {\n        get: function () {\n            return 9;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return Document;\n}(NodeWithChildren));\nexports.Document = Document;\n/**\n * An element within the DOM.\n */\nvar Element = /** @class */ (function (_super) {\n    __extends(Element, _super);\n    /**\n     * @param name Name of the tag, eg. `div`, `span`.\n     * @param attribs Object mapping attribute names to attribute values.\n     * @param children Children of the node.\n     */\n    function Element(name, attribs, children, type) {\n        if (children === void 0) { children = []; }\n        if (type === void 0) { type = name === \"script\"\n            ? domelementtype_1.ElementType.Script\n            : name === \"style\"\n                ? domelementtype_1.ElementType.Style\n                : domelementtype_1.ElementType.Tag; }\n        var _this = _super.call(this, children) || this;\n        _this.name = name;\n        _this.attribs = attribs;\n        _this.type = type;\n        return _this;\n    }\n    Object.defineProperty(Element.prototype, \"nodeType\", {\n        get: function () {\n            return 1;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Element.prototype, \"tagName\", {\n        // DOM Level 1 aliases\n        /**\n         * Same as {@link name}.\n         * [DOM spec](https://dom.spec.whatwg.org)-compatible alias.\n         */\n        get: function () {\n            return this.name;\n        },\n        set: function (name) {\n            this.name = name;\n        },\n        enumerable: false,\n        configurable: true\n    });\n    Object.defineProperty(Element.prototype, \"attributes\", {\n        get: function () {\n            var _this = this;\n            return Object.keys(this.attribs).map(function (name) {\n                var _a, _b;\n                return ({\n                    name: name,\n                    value: _this.attribs[name],\n                    namespace: (_a = _this[\"x-attribsNamespace\"]) === null || _a === void 0 ? void 0 : _a[name],\n                    prefix: (_b = _this[\"x-attribsPrefix\"]) === null || _b === void 0 ? void 0 : _b[name],\n                });\n            });\n        },\n        enumerable: false,\n        configurable: true\n    });\n    return Element;\n}(NodeWithChildren));\nexports.Element = Element;\n/**\n * @param node Node to check.\n * @returns `true` if the node is a `Element`, `false` otherwise.\n */\nfunction isTag(node) {\n    return (0, domelementtype_1.isTag)(node);\n}\nexports.isTag = isTag;\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `CDATA`, `false` otherwise.\n */\nfunction isCDATA(node) {\n    return node.type === domelementtype_1.ElementType.CDATA;\n}\nexports.isCDATA = isCDATA;\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `Text`, `false` otherwise.\n */\nfunction isText(node) {\n    return node.type === domelementtype_1.ElementType.Text;\n}\nexports.isText = isText;\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `Comment`, `false` otherwise.\n */\nfunction isComment(node) {\n    return node.type === domelementtype_1.ElementType.Comment;\n}\nexports.isComment = isComment;\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `ProcessingInstruction`, `false` otherwise.\n */\nfunction isDirective(node) {\n    return node.type === domelementtype_1.ElementType.Directive;\n}\nexports.isDirective = isDirective;\n/**\n * @param node Node to check.\n * @returns `true` if the node has the type `ProcessingInstruction`, `false` otherwise.\n */\nfunction isDocument(node) {\n    return node.type === domelementtype_1.ElementType.Root;\n}\nexports.isDocument = isDocument;\n/**\n * @param node Node to check.\n * @returns `true` if the node has children, `false` otherwise.\n */\nfunction hasChildren(node) {\n    return Object.prototype.hasOwnProperty.call(node, \"children\");\n}\nexports.hasChildren = hasChildren;\n/**\n * Clone a node, and optionally its children.\n *\n * @param recursive Clone child nodes as well.\n * @returns A clone of the node.\n */\nfunction cloneNode(node, recursive) {\n    if (recursive === void 0) { recursive = false; }\n    var result;\n    if (isText(node)) {\n        result = new Text(node.data);\n    }\n    else if (isComment(node)) {\n        result = new Comment(node.data);\n    }\n    else if (isTag(node)) {\n        var children = recursive ? cloneChildren(node.children) : [];\n        var clone_1 = new Element(node.name, __assign({}, node.attribs), children);\n        children.forEach(function (child) { return (child.parent = clone_1); });\n        if (node.namespace != null) {\n            clone_1.namespace = node.namespace;\n        }\n        if (node[\"x-attribsNamespace\"]) {\n            clone_1[\"x-attribsNamespace\"] = __assign({}, node[\"x-attribsNamespace\"]);\n        }\n        if (node[\"x-attribsPrefix\"]) {\n            clone_1[\"x-attribsPrefix\"] = __assign({}, node[\"x-attribsPrefix\"]);\n        }\n        result = clone_1;\n    }\n    else if (isCDATA(node)) {\n        var children = recursive ? cloneChildren(node.children) : [];\n        var clone_2 = new CDATA(children);\n        children.forEach(function (child) { return (child.parent = clone_2); });\n        result = clone_2;\n    }\n    else if (isDocument(node)) {\n        var children = recursive ? cloneChildren(node.children) : [];\n        var clone_3 = new Document(children);\n        children.forEach(function (child) { return (child.parent = clone_3); });\n        if (node[\"x-mode\"]) {\n            clone_3[\"x-mode\"] = node[\"x-mode\"];\n        }\n        result = clone_3;\n    }\n    else if (isDirective(node)) {\n        var instruction = new ProcessingInstruction(node.name, node.data);\n        if (node[\"x-name\"] != null) {\n            instruction[\"x-name\"] = node[\"x-name\"];\n            instruction[\"x-publicId\"] = node[\"x-publicId\"];\n            instruction[\"x-systemId\"] = node[\"x-systemId\"];\n        }\n        result = instruction;\n    }\n    else {\n        throw new Error(\"Not implemented yet: \".concat(node.type));\n    }\n    result.startIndex = node.startIndex;\n    result.endIndex = node.endIndex;\n    if (node.sourceCodeLocation != null) {\n        result.sourceCodeLocation = node.sourceCodeLocation;\n    }\n    return result;\n}\nexports.cloneNode = cloneNode;\nfunction cloneChildren(childs) {\n    var children = childs.map(function (child) { return cloneNode(child, true); });\n    for (var i = 1; i < children.length; i++) {\n        children[i].prev = children[i - 1];\n        children[i - 1].next = children[i];\n    }\n    return children;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/domhandler/lib/node.js\n");

/***/ })

};
;