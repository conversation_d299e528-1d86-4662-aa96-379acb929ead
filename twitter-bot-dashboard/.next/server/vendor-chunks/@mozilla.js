/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@mozilla";
exports.ids = ["vendor-chunks/@mozilla"];
exports.modules = {

/***/ "(rsc)/./node_modules/@mozilla/readability/Readability-readerable.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@mozilla/readability/Readability-readerable.js ***!
  \*********************************************************************/
/***/ ((module) => {

eval("/*\n * Copyright (c) 2010 Arc90 Inc\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/*\n * This code is heavily based on Arc90's readability.js (1.7.1) script\n * available at: http://code.google.com/p/arc90labs-readability\n */\n\nvar REGEXPS = {\n  // NOTE: These two regular expressions are duplicated in\n  // Readability.js. Please keep both copies in sync.\n  unlikelyCandidates:\n    /-ad-|ai2html|banner|breadcrumbs|combx|comment|community|cover-wrap|disqus|extra|footer|gdpr|header|legends|menu|related|remark|replies|rss|shoutbox|sidebar|skyscraper|social|sponsor|supplemental|ad-break|agegate|pagination|pager|popup|yom-remote/i,\n  okMaybeItsACandidate: /and|article|body|column|content|main|shadow/i,\n};\n\nfunction isNodeVisible(node) {\n  // Have to null-check node.style and node.className.includes to deal with SVG and MathML nodes.\n  return (\n    (!node.style || node.style.display != \"none\") &&\n    !node.hasAttribute(\"hidden\") &&\n    //check for \"fallback-image\" so that wikimedia math images are displayed\n    (!node.hasAttribute(\"aria-hidden\") ||\n      node.getAttribute(\"aria-hidden\") != \"true\" ||\n      (node.className &&\n        node.className.includes &&\n        node.className.includes(\"fallback-image\")))\n  );\n}\n\n/**\n * Decides whether or not the document is reader-able without parsing the whole thing.\n * @param {Object} options Configuration object.\n * @param {number} [options.minContentLength=140] The minimum node content length used to decide if the document is readerable.\n * @param {number} [options.minScore=20] The minumum cumulated 'score' used to determine if the document is readerable.\n * @param {Function} [options.visibilityChecker=isNodeVisible] The function used to determine if a node is visible.\n * @return {boolean} Whether or not we suspect Readability.parse() will suceeed at returning an article object.\n */\nfunction isProbablyReaderable(doc, options = {}) {\n  // For backward compatibility reasons 'options' can either be a configuration object or the function used\n  // to determine if a node is visible.\n  if (typeof options == \"function\") {\n    options = { visibilityChecker: options };\n  }\n\n  var defaultOptions = {\n    minScore: 20,\n    minContentLength: 140,\n    visibilityChecker: isNodeVisible,\n  };\n  options = Object.assign(defaultOptions, options);\n\n  var nodes = doc.querySelectorAll(\"p, pre, article\");\n\n  // Get <div> nodes which have <br> node(s) and append them into the `nodes` variable.\n  // Some articles' DOM structures might look like\n  // <div>\n  //   Sentences<br>\n  //   <br>\n  //   Sentences<br>\n  // </div>\n  var brNodes = doc.querySelectorAll(\"div > br\");\n  if (brNodes.length) {\n    var set = new Set(nodes);\n    [].forEach.call(brNodes, function (node) {\n      set.add(node.parentNode);\n    });\n    nodes = Array.from(set);\n  }\n\n  var score = 0;\n  // This is a little cheeky, we use the accumulator 'score' to decide what to return from\n  // this callback:\n  return [].some.call(nodes, function (node) {\n    if (!options.visibilityChecker(node)) {\n      return false;\n    }\n\n    var matchString = node.className + \" \" + node.id;\n    if (\n      REGEXPS.unlikelyCandidates.test(matchString) &&\n      !REGEXPS.okMaybeItsACandidate.test(matchString)\n    ) {\n      return false;\n    }\n\n    if (node.matches(\"li p\")) {\n      return false;\n    }\n\n    var textContentLength = node.textContent.trim().length;\n    if (textContentLength < options.minContentLength) {\n      return false;\n    }\n\n    score += Math.sqrt(textContentLength - options.minContentLength);\n\n    if (score > options.minScore) {\n      return true;\n    }\n    return false;\n  });\n}\n\nif (true) {\n  /* eslint-disable-next-line no-redeclare */\n  /* global module */\n  module.exports = isProbablyReaderable;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@mozilla/readability/Readability-readerable.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@mozilla/readability/Readability.js":
/*!**********************************************************!*\
  !*** ./node_modules/@mozilla/readability/Readability.js ***!
  \**********************************************************/
/***/ ((module) => {

eval("/*\n * Copyright (c) 2010 Arc90 Inc\n *\n * Licensed under the Apache License, Version 2.0 (the \"License\");\n * you may not use this file except in compliance with the License.\n * You may obtain a copy of the License at\n *\n *     http://www.apache.org/licenses/LICENSE-2.0\n *\n * Unless required by applicable law or agreed to in writing, software\n * distributed under the License is distributed on an \"AS IS\" BASIS,\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\n * See the License for the specific language governing permissions and\n * limitations under the License.\n */\n\n/*\n * This code is heavily based on Arc90's readability.js (1.7.1) script\n * available at: http://code.google.com/p/arc90labs-readability\n */\n\n/**\n * Public constructor.\n * @param {HTMLDocument} doc     The document to parse.\n * @param {Object}       options The options object.\n */\nfunction Readability(doc, options) {\n  // In some older versions, people passed a URI as the first argument. Cope:\n  if (options && options.documentElement) {\n    doc = options;\n    options = arguments[2];\n  } else if (!doc || !doc.documentElement) {\n    throw new Error(\n      \"First argument to Readability constructor should be a document object.\"\n    );\n  }\n  options = options || {};\n\n  this._doc = doc;\n  this._docJSDOMParser = this._doc.firstChild.__JSDOMParser__;\n  this._articleTitle = null;\n  this._articleByline = null;\n  this._articleDir = null;\n  this._articleSiteName = null;\n  this._attempts = [];\n  this._metadata = {};\n\n  // Configurable options\n  this._debug = !!options.debug;\n  this._maxElemsToParse =\n    options.maxElemsToParse || this.DEFAULT_MAX_ELEMS_TO_PARSE;\n  this._nbTopCandidates =\n    options.nbTopCandidates || this.DEFAULT_N_TOP_CANDIDATES;\n  this._charThreshold = options.charThreshold || this.DEFAULT_CHAR_THRESHOLD;\n  this._classesToPreserve = this.CLASSES_TO_PRESERVE.concat(\n    options.classesToPreserve || []\n  );\n  this._keepClasses = !!options.keepClasses;\n  this._serializer =\n    options.serializer ||\n    function (el) {\n      return el.innerHTML;\n    };\n  this._disableJSONLD = !!options.disableJSONLD;\n  this._allowedVideoRegex = options.allowedVideoRegex || this.REGEXPS.videos;\n  this._linkDensityModifier = options.linkDensityModifier || 0;\n\n  // Start with all flags set\n  this._flags =\n    this.FLAG_STRIP_UNLIKELYS |\n    this.FLAG_WEIGHT_CLASSES |\n    this.FLAG_CLEAN_CONDITIONALLY;\n\n  // Control whether log messages are sent to the console\n  if (this._debug) {\n    let logNode = function (node) {\n      if (node.nodeType == node.TEXT_NODE) {\n        return `${node.nodeName} (\"${node.textContent}\")`;\n      }\n      let attrPairs = Array.from(node.attributes || [], function (attr) {\n        return `${attr.name}=\"${attr.value}\"`;\n      }).join(\" \");\n      return `<${node.localName} ${attrPairs}>`;\n    };\n    this.log = function () {\n      if (typeof console !== \"undefined\") {\n        let args = Array.from(arguments, arg => {\n          if (arg && arg.nodeType == this.ELEMENT_NODE) {\n            return logNode(arg);\n          }\n          return arg;\n        });\n        args.unshift(\"Reader: (Readability)\");\n        // eslint-disable-next-line no-console\n        console.log(...args);\n      } else if (typeof dump !== \"undefined\") {\n        /* global dump */\n        var msg = Array.prototype.map\n          .call(arguments, function (x) {\n            return x && x.nodeName ? logNode(x) : x;\n          })\n          .join(\" \");\n        dump(\"Reader: (Readability) \" + msg + \"\\n\");\n      }\n    };\n  } else {\n    this.log = function () {};\n  }\n}\n\nReadability.prototype = {\n  FLAG_STRIP_UNLIKELYS: 0x1,\n  FLAG_WEIGHT_CLASSES: 0x2,\n  FLAG_CLEAN_CONDITIONALLY: 0x4,\n\n  // https://developer.mozilla.org/en-US/docs/Web/API/Node/nodeType\n  ELEMENT_NODE: 1,\n  TEXT_NODE: 3,\n\n  // Max number of nodes supported by this parser. Default: 0 (no limit)\n  DEFAULT_MAX_ELEMS_TO_PARSE: 0,\n\n  // The number of top candidates to consider when analysing how\n  // tight the competition is among candidates.\n  DEFAULT_N_TOP_CANDIDATES: 5,\n\n  // Element tags to score by default.\n  DEFAULT_TAGS_TO_SCORE: \"section,h2,h3,h4,h5,h6,p,td,pre\"\n    .toUpperCase()\n    .split(\",\"),\n\n  // The default number of chars an article must have in order to return a result\n  DEFAULT_CHAR_THRESHOLD: 500,\n\n  // All of the regular expressions in use within readability.\n  // Defined up here so we don't instantiate them repeatedly in loops.\n  REGEXPS: {\n    // NOTE: These two regular expressions are duplicated in\n    // Readability-readerable.js. Please keep both copies in sync.\n    unlikelyCandidates:\n      /-ad-|ai2html|banner|breadcrumbs|combx|comment|community|cover-wrap|disqus|extra|footer|gdpr|header|legends|menu|related|remark|replies|rss|shoutbox|sidebar|skyscraper|social|sponsor|supplemental|ad-break|agegate|pagination|pager|popup|yom-remote/i,\n    okMaybeItsACandidate: /and|article|body|column|content|main|shadow/i,\n\n    positive:\n      /article|body|content|entry|hentry|h-entry|main|page|pagination|post|text|blog|story/i,\n    negative:\n      /-ad-|hidden|^hid$| hid$| hid |^hid |banner|combx|comment|com-|contact|footer|gdpr|masthead|media|meta|outbrain|promo|related|scroll|share|shoutbox|sidebar|skyscraper|sponsor|shopping|tags|widget/i,\n    extraneous:\n      /print|archive|comment|discuss|e[\\-]?mail|share|reply|all|login|sign|single|utility/i,\n    byline: /byline|author|dateline|writtenby|p-author/i,\n    replaceFonts: /<(\\/?)font[^>]*>/gi,\n    normalize: /\\s{2,}/g,\n    videos:\n      /\\/\\/(www\\.)?((dailymotion|youtube|youtube-nocookie|player\\.vimeo|v\\.qq)\\.com|(archive|upload\\.wikimedia)\\.org|player\\.twitch\\.tv)/i,\n    shareElements: /(\\b|_)(share|sharedaddy)(\\b|_)/i,\n    nextLink: /(next|weiter|continue|>([^\\|]|$)|»([^\\|]|$))/i,\n    prevLink: /(prev|earl|old|new|<|«)/i,\n    tokenize: /\\W+/g,\n    whitespace: /^\\s*$/,\n    hasContent: /\\S$/,\n    hashUrl: /^#.+/,\n    srcsetUrl: /(\\S+)(\\s+[\\d.]+[xw])?(\\s*(?:,|$))/g,\n    b64DataUrl: /^data:\\s*([^\\s;,]+)\\s*;\\s*base64\\s*,/i,\n    // Commas as used in Latin, Sindhi, Chinese and various other scripts.\n    // see: https://en.wikipedia.org/wiki/Comma#Comma_variants\n    commas: /\\u002C|\\u060C|\\uFE50|\\uFE10|\\uFE11|\\u2E41|\\u2E34|\\u2E32|\\uFF0C/g,\n    // See: https://schema.org/Article\n    jsonLdArticleTypes:\n      /^Article|AdvertiserContentArticle|NewsArticle|AnalysisNewsArticle|AskPublicNewsArticle|BackgroundNewsArticle|OpinionNewsArticle|ReportageNewsArticle|ReviewNewsArticle|Report|SatiricalArticle|ScholarlyArticle|MedicalScholarlyArticle|SocialMediaPosting|BlogPosting|LiveBlogPosting|DiscussionForumPosting|TechArticle|APIReference$/,\n    // used to see if a node's content matches words commonly used for ad blocks or loading indicators\n    adWords:\n      /^(ad(vertising|vertisement)?|pub(licité)?|werb(ung)?|广告|Реклама|Anuncio)$/iu,\n    loadingWords:\n      /^((loading|正在加载|Загрузка|chargement|cargando)(…|\\.\\.\\.)?)$/iu,\n  },\n\n  UNLIKELY_ROLES: [\n    \"menu\",\n    \"menubar\",\n    \"complementary\",\n    \"navigation\",\n    \"alert\",\n    \"alertdialog\",\n    \"dialog\",\n  ],\n\n  DIV_TO_P_ELEMS: new Set([\n    \"BLOCKQUOTE\",\n    \"DL\",\n    \"DIV\",\n    \"IMG\",\n    \"OL\",\n    \"P\",\n    \"PRE\",\n    \"TABLE\",\n    \"UL\",\n  ]),\n\n  ALTER_TO_DIV_EXCEPTIONS: [\"DIV\", \"ARTICLE\", \"SECTION\", \"P\", \"OL\", \"UL\"],\n\n  PRESENTATIONAL_ATTRIBUTES: [\n    \"align\",\n    \"background\",\n    \"bgcolor\",\n    \"border\",\n    \"cellpadding\",\n    \"cellspacing\",\n    \"frame\",\n    \"hspace\",\n    \"rules\",\n    \"style\",\n    \"valign\",\n    \"vspace\",\n  ],\n\n  DEPRECATED_SIZE_ATTRIBUTE_ELEMS: [\"TABLE\", \"TH\", \"TD\", \"HR\", \"PRE\"],\n\n  // The commented out elements qualify as phrasing content but tend to be\n  // removed by readability when put into paragraphs, so we ignore them here.\n  PHRASING_ELEMS: [\n    // \"CANVAS\", \"IFRAME\", \"SVG\", \"VIDEO\",\n    \"ABBR\",\n    \"AUDIO\",\n    \"B\",\n    \"BDO\",\n    \"BR\",\n    \"BUTTON\",\n    \"CITE\",\n    \"CODE\",\n    \"DATA\",\n    \"DATALIST\",\n    \"DFN\",\n    \"EM\",\n    \"EMBED\",\n    \"I\",\n    \"IMG\",\n    \"INPUT\",\n    \"KBD\",\n    \"LABEL\",\n    \"MARK\",\n    \"MATH\",\n    \"METER\",\n    \"NOSCRIPT\",\n    \"OBJECT\",\n    \"OUTPUT\",\n    \"PROGRESS\",\n    \"Q\",\n    \"RUBY\",\n    \"SAMP\",\n    \"SCRIPT\",\n    \"SELECT\",\n    \"SMALL\",\n    \"SPAN\",\n    \"STRONG\",\n    \"SUB\",\n    \"SUP\",\n    \"TEXTAREA\",\n    \"TIME\",\n    \"VAR\",\n    \"WBR\",\n  ],\n\n  // These are the classes that readability sets itself.\n  CLASSES_TO_PRESERVE: [\"page\"],\n\n  // These are the list of HTML entities that need to be escaped.\n  HTML_ESCAPE_MAP: {\n    lt: \"<\",\n    gt: \">\",\n    amp: \"&\",\n    quot: '\"',\n    apos: \"'\",\n  },\n\n  /**\n   * Run any post-process modifications to article content as necessary.\n   *\n   * @param Element\n   * @return void\n   **/\n  _postProcessContent(articleContent) {\n    // Readability cannot open relative uris so we convert them to absolute uris.\n    this._fixRelativeUris(articleContent);\n\n    this._simplifyNestedElements(articleContent);\n\n    if (!this._keepClasses) {\n      // Remove classes.\n      this._cleanClasses(articleContent);\n    }\n  },\n\n  /**\n   * Iterates over a NodeList, calls `filterFn` for each node and removes node\n   * if function returned `true`.\n   *\n   * If function is not passed, removes all the nodes in node list.\n   *\n   * @param NodeList nodeList The nodes to operate on\n   * @param Function filterFn the function to use as a filter\n   * @return void\n   */\n  _removeNodes(nodeList, filterFn) {\n    // Avoid ever operating on live node lists.\n    if (this._docJSDOMParser && nodeList._isLiveNodeList) {\n      throw new Error(\"Do not pass live node lists to _removeNodes\");\n    }\n    for (var i = nodeList.length - 1; i >= 0; i--) {\n      var node = nodeList[i];\n      var parentNode = node.parentNode;\n      if (parentNode) {\n        if (!filterFn || filterFn.call(this, node, i, nodeList)) {\n          parentNode.removeChild(node);\n        }\n      }\n    }\n  },\n\n  /**\n   * Iterates over a NodeList, and calls _setNodeTag for each node.\n   *\n   * @param NodeList nodeList The nodes to operate on\n   * @param String newTagName the new tag name to use\n   * @return void\n   */\n  _replaceNodeTags(nodeList, newTagName) {\n    // Avoid ever operating on live node lists.\n    if (this._docJSDOMParser && nodeList._isLiveNodeList) {\n      throw new Error(\"Do not pass live node lists to _replaceNodeTags\");\n    }\n    for (const node of nodeList) {\n      this._setNodeTag(node, newTagName);\n    }\n  },\n\n  /**\n   * Iterate over a NodeList, which doesn't natively fully implement the Array\n   * interface.\n   *\n   * For convenience, the current object context is applied to the provided\n   * iterate function.\n   *\n   * @param  NodeList nodeList The NodeList.\n   * @param  Function fn       The iterate function.\n   * @return void\n   */\n  _forEachNode(nodeList, fn) {\n    Array.prototype.forEach.call(nodeList, fn, this);\n  },\n\n  /**\n   * Iterate over a NodeList, and return the first node that passes\n   * the supplied test function\n   *\n   * For convenience, the current object context is applied to the provided\n   * test function.\n   *\n   * @param  NodeList nodeList The NodeList.\n   * @param  Function fn       The test function.\n   * @return void\n   */\n  _findNode(nodeList, fn) {\n    return Array.prototype.find.call(nodeList, fn, this);\n  },\n\n  /**\n   * Iterate over a NodeList, return true if any of the provided iterate\n   * function calls returns true, false otherwise.\n   *\n   * For convenience, the current object context is applied to the\n   * provided iterate function.\n   *\n   * @param  NodeList nodeList The NodeList.\n   * @param  Function fn       The iterate function.\n   * @return Boolean\n   */\n  _someNode(nodeList, fn) {\n    return Array.prototype.some.call(nodeList, fn, this);\n  },\n\n  /**\n   * Iterate over a NodeList, return true if all of the provided iterate\n   * function calls return true, false otherwise.\n   *\n   * For convenience, the current object context is applied to the\n   * provided iterate function.\n   *\n   * @param  NodeList nodeList The NodeList.\n   * @param  Function fn       The iterate function.\n   * @return Boolean\n   */\n  _everyNode(nodeList, fn) {\n    return Array.prototype.every.call(nodeList, fn, this);\n  },\n\n  _getAllNodesWithTag(node, tagNames) {\n    if (node.querySelectorAll) {\n      return node.querySelectorAll(tagNames.join(\",\"));\n    }\n    return [].concat.apply(\n      [],\n      tagNames.map(function (tag) {\n        var collection = node.getElementsByTagName(tag);\n        return Array.isArray(collection) ? collection : Array.from(collection);\n      })\n    );\n  },\n\n  /**\n   * Removes the class=\"\" attribute from every element in the given\n   * subtree, except those that match CLASSES_TO_PRESERVE and\n   * the classesToPreserve array from the options object.\n   *\n   * @param Element\n   * @return void\n   */\n  _cleanClasses(node) {\n    var classesToPreserve = this._classesToPreserve;\n    var className = (node.getAttribute(\"class\") || \"\")\n      .split(/\\s+/)\n      .filter(cls => classesToPreserve.includes(cls))\n      .join(\" \");\n\n    if (className) {\n      node.setAttribute(\"class\", className);\n    } else {\n      node.removeAttribute(\"class\");\n    }\n\n    for (node = node.firstElementChild; node; node = node.nextElementSibling) {\n      this._cleanClasses(node);\n    }\n  },\n\n  /**\n   * Tests whether a string is a URL or not.\n   *\n   * @param {string} str The string to test\n   * @return {boolean} true if str is a URL, false if not\n   */\n  _isUrl(str) {\n    try {\n      new URL(str);\n      return true;\n    } catch {\n      return false;\n    }\n  },\n  /**\n   * Converts each <a> and <img> uri in the given element to an absolute URI,\n   * ignoring #ref URIs.\n   *\n   * @param Element\n   * @return void\n   */\n  _fixRelativeUris(articleContent) {\n    var baseURI = this._doc.baseURI;\n    var documentURI = this._doc.documentURI;\n    function toAbsoluteURI(uri) {\n      // Leave hash links alone if the base URI matches the document URI:\n      if (baseURI == documentURI && uri.charAt(0) == \"#\") {\n        return uri;\n      }\n\n      // Otherwise, resolve against base URI:\n      try {\n        return new URL(uri, baseURI).href;\n      } catch (ex) {\n        // Something went wrong, just return the original:\n      }\n      return uri;\n    }\n\n    var links = this._getAllNodesWithTag(articleContent, [\"a\"]);\n    this._forEachNode(links, function (link) {\n      var href = link.getAttribute(\"href\");\n      if (href) {\n        // Remove links with javascript: URIs, since\n        // they won't work after scripts have been removed from the page.\n        if (href.indexOf(\"javascript:\") === 0) {\n          // if the link only contains simple text content, it can be converted to a text node\n          if (\n            link.childNodes.length === 1 &&\n            link.childNodes[0].nodeType === this.TEXT_NODE\n          ) {\n            var text = this._doc.createTextNode(link.textContent);\n            link.parentNode.replaceChild(text, link);\n          } else {\n            // if the link has multiple children, they should all be preserved\n            var container = this._doc.createElement(\"span\");\n            while (link.firstChild) {\n              container.appendChild(link.firstChild);\n            }\n            link.parentNode.replaceChild(container, link);\n          }\n        } else {\n          link.setAttribute(\"href\", toAbsoluteURI(href));\n        }\n      }\n    });\n\n    var medias = this._getAllNodesWithTag(articleContent, [\n      \"img\",\n      \"picture\",\n      \"figure\",\n      \"video\",\n      \"audio\",\n      \"source\",\n    ]);\n\n    this._forEachNode(medias, function (media) {\n      var src = media.getAttribute(\"src\");\n      var poster = media.getAttribute(\"poster\");\n      var srcset = media.getAttribute(\"srcset\");\n\n      if (src) {\n        media.setAttribute(\"src\", toAbsoluteURI(src));\n      }\n\n      if (poster) {\n        media.setAttribute(\"poster\", toAbsoluteURI(poster));\n      }\n\n      if (srcset) {\n        var newSrcset = srcset.replace(\n          this.REGEXPS.srcsetUrl,\n          function (_, p1, p2, p3) {\n            return toAbsoluteURI(p1) + (p2 || \"\") + p3;\n          }\n        );\n\n        media.setAttribute(\"srcset\", newSrcset);\n      }\n    });\n  },\n\n  _simplifyNestedElements(articleContent) {\n    var node = articleContent;\n\n    while (node) {\n      if (\n        node.parentNode &&\n        [\"DIV\", \"SECTION\"].includes(node.tagName) &&\n        !(node.id && node.id.startsWith(\"readability\"))\n      ) {\n        if (this._isElementWithoutContent(node)) {\n          node = this._removeAndGetNext(node);\n          continue;\n        } else if (\n          this._hasSingleTagInsideElement(node, \"DIV\") ||\n          this._hasSingleTagInsideElement(node, \"SECTION\")\n        ) {\n          var child = node.children[0];\n          for (var i = 0; i < node.attributes.length; i++) {\n            child.setAttributeNode(node.attributes[i].cloneNode());\n          }\n          node.parentNode.replaceChild(child, node);\n          node = child;\n          continue;\n        }\n      }\n\n      node = this._getNextNode(node);\n    }\n  },\n\n  /**\n   * Get the article title as an H1.\n   *\n   * @return string\n   **/\n  _getArticleTitle() {\n    var doc = this._doc;\n    var curTitle = \"\";\n    var origTitle = \"\";\n\n    try {\n      curTitle = origTitle = doc.title.trim();\n\n      // If they had an element with id \"title\" in their HTML\n      if (typeof curTitle !== \"string\") {\n        curTitle = origTitle = this._getInnerText(\n          doc.getElementsByTagName(\"title\")[0]\n        );\n      }\n    } catch (e) {\n      /* ignore exceptions setting the title. */\n    }\n\n    var titleHadHierarchicalSeparators = false;\n    function wordCount(str) {\n      return str.split(/\\s+/).length;\n    }\n\n    // If there's a separator in the title, first remove the final part\n    if (/ [\\|\\-\\\\\\/>»] /.test(curTitle)) {\n      titleHadHierarchicalSeparators = / [\\\\\\/>»] /.test(curTitle);\n      let allSeparators = Array.from(origTitle.matchAll(/ [\\|\\-\\\\\\/>»] /gi));\n      curTitle = origTitle.substring(0, allSeparators.pop().index);\n\n      // If the resulting title is too short, remove the first part instead:\n      if (wordCount(curTitle) < 3) {\n        curTitle = origTitle.replace(/^[^\\|\\-\\\\\\/>»]*[\\|\\-\\\\\\/>»]/gi, \"\");\n      }\n    } else if (curTitle.includes(\": \")) {\n      // Check if we have an heading containing this exact string, so we\n      // could assume it's the full title.\n      var headings = this._getAllNodesWithTag(doc, [\"h1\", \"h2\"]);\n      var trimmedTitle = curTitle.trim();\n      var match = this._someNode(headings, function (heading) {\n        return heading.textContent.trim() === trimmedTitle;\n      });\n\n      // If we don't, let's extract the title out of the original title string.\n      if (!match) {\n        curTitle = origTitle.substring(origTitle.lastIndexOf(\":\") + 1);\n\n        // If the title is now too short, try the first colon instead:\n        if (wordCount(curTitle) < 3) {\n          curTitle = origTitle.substring(origTitle.indexOf(\":\") + 1);\n          // But if we have too many words before the colon there's something weird\n          // with the titles and the H tags so let's just use the original title instead\n        } else if (wordCount(origTitle.substr(0, origTitle.indexOf(\":\"))) > 5) {\n          curTitle = origTitle;\n        }\n      }\n    } else if (curTitle.length > 150 || curTitle.length < 15) {\n      var hOnes = doc.getElementsByTagName(\"h1\");\n\n      if (hOnes.length === 1) {\n        curTitle = this._getInnerText(hOnes[0]);\n      }\n    }\n\n    curTitle = curTitle.trim().replace(this.REGEXPS.normalize, \" \");\n    // If we now have 4 words or fewer as our title, and either no\n    // 'hierarchical' separators (\\, /, > or ») were found in the original\n    // title or we decreased the number of words by more than 1 word, use\n    // the original title.\n    var curTitleWordCount = wordCount(curTitle);\n    if (\n      curTitleWordCount <= 4 &&\n      (!titleHadHierarchicalSeparators ||\n        curTitleWordCount !=\n          wordCount(origTitle.replace(/[\\|\\-\\\\\\/>»]+/g, \"\")) - 1)\n    ) {\n      curTitle = origTitle;\n    }\n\n    return curTitle;\n  },\n\n  /**\n   * Prepare the HTML document for readability to scrape it.\n   * This includes things like stripping javascript, CSS, and handling terrible markup.\n   *\n   * @return void\n   **/\n  _prepDocument() {\n    var doc = this._doc;\n\n    // Remove all style tags in head\n    this._removeNodes(this._getAllNodesWithTag(doc, [\"style\"]));\n\n    if (doc.body) {\n      this._replaceBrs(doc.body);\n    }\n\n    this._replaceNodeTags(this._getAllNodesWithTag(doc, [\"font\"]), \"SPAN\");\n  },\n\n  /**\n   * Finds the next node, starting from the given node, and ignoring\n   * whitespace in between. If the given node is an element, the same node is\n   * returned.\n   */\n  _nextNode(node) {\n    var next = node;\n    while (\n      next &&\n      next.nodeType != this.ELEMENT_NODE &&\n      this.REGEXPS.whitespace.test(next.textContent)\n    ) {\n      next = next.nextSibling;\n    }\n    return next;\n  },\n\n  /**\n   * Replaces 2 or more successive <br> elements with a single <p>.\n   * Whitespace between <br> elements are ignored. For example:\n   *   <div>foo<br>bar<br> <br><br>abc</div>\n   * will become:\n   *   <div>foo<br>bar<p>abc</p></div>\n   */\n  _replaceBrs(elem) {\n    this._forEachNode(this._getAllNodesWithTag(elem, [\"br\"]), function (br) {\n      var next = br.nextSibling;\n\n      // Whether 2 or more <br> elements have been found and replaced with a\n      // <p> block.\n      var replaced = false;\n\n      // If we find a <br> chain, remove the <br>s until we hit another node\n      // or non-whitespace. This leaves behind the first <br> in the chain\n      // (which will be replaced with a <p> later).\n      while ((next = this._nextNode(next)) && next.tagName == \"BR\") {\n        replaced = true;\n        var brSibling = next.nextSibling;\n        next.remove();\n        next = brSibling;\n      }\n\n      // If we removed a <br> chain, replace the remaining <br> with a <p>. Add\n      // all sibling nodes as children of the <p> until we hit another <br>\n      // chain.\n      if (replaced) {\n        var p = this._doc.createElement(\"p\");\n        br.parentNode.replaceChild(p, br);\n\n        next = p.nextSibling;\n        while (next) {\n          // If we've hit another <br><br>, we're done adding children to this <p>.\n          if (next.tagName == \"BR\") {\n            var nextElem = this._nextNode(next.nextSibling);\n            if (nextElem && nextElem.tagName == \"BR\") {\n              break;\n            }\n          }\n\n          if (!this._isPhrasingContent(next)) {\n            break;\n          }\n\n          // Otherwise, make this node a child of the new <p>.\n          var sibling = next.nextSibling;\n          p.appendChild(next);\n          next = sibling;\n        }\n\n        while (p.lastChild && this._isWhitespace(p.lastChild)) {\n          p.lastChild.remove();\n        }\n\n        if (p.parentNode.tagName === \"P\") {\n          this._setNodeTag(p.parentNode, \"DIV\");\n        }\n      }\n    });\n  },\n\n  _setNodeTag(node, tag) {\n    this.log(\"_setNodeTag\", node, tag);\n    if (this._docJSDOMParser) {\n      node.localName = tag.toLowerCase();\n      node.tagName = tag.toUpperCase();\n      return node;\n    }\n\n    var replacement = node.ownerDocument.createElement(tag);\n    while (node.firstChild) {\n      replacement.appendChild(node.firstChild);\n    }\n    node.parentNode.replaceChild(replacement, node);\n    if (node.readability) {\n      replacement.readability = node.readability;\n    }\n\n    for (var i = 0; i < node.attributes.length; i++) {\n      replacement.setAttributeNode(node.attributes[i].cloneNode());\n    }\n    return replacement;\n  },\n\n  /**\n   * Prepare the article node for display. Clean out any inline styles,\n   * iframes, forms, strip extraneous <p> tags, etc.\n   *\n   * @param Element\n   * @return void\n   **/\n  _prepArticle(articleContent) {\n    this._cleanStyles(articleContent);\n\n    // Check for data tables before we continue, to avoid removing items in\n    // those tables, which will often be isolated even though they're\n    // visually linked to other content-ful elements (text, images, etc.).\n    this._markDataTables(articleContent);\n\n    this._fixLazyImages(articleContent);\n\n    // Clean out junk from the article content\n    this._cleanConditionally(articleContent, \"form\");\n    this._cleanConditionally(articleContent, \"fieldset\");\n    this._clean(articleContent, \"object\");\n    this._clean(articleContent, \"embed\");\n    this._clean(articleContent, \"footer\");\n    this._clean(articleContent, \"link\");\n    this._clean(articleContent, \"aside\");\n\n    // Clean out elements with little content that have \"share\" in their id/class combinations from final top candidates,\n    // which means we don't remove the top candidates even they have \"share\".\n\n    var shareElementThreshold = this.DEFAULT_CHAR_THRESHOLD;\n\n    this._forEachNode(articleContent.children, function (topCandidate) {\n      this._cleanMatchedNodes(topCandidate, function (node, matchString) {\n        return (\n          this.REGEXPS.shareElements.test(matchString) &&\n          node.textContent.length < shareElementThreshold\n        );\n      });\n    });\n\n    this._clean(articleContent, \"iframe\");\n    this._clean(articleContent, \"input\");\n    this._clean(articleContent, \"textarea\");\n    this._clean(articleContent, \"select\");\n    this._clean(articleContent, \"button\");\n    this._cleanHeaders(articleContent);\n\n    // Do these last as the previous stuff may have removed junk\n    // that will affect these\n    this._cleanConditionally(articleContent, \"table\");\n    this._cleanConditionally(articleContent, \"ul\");\n    this._cleanConditionally(articleContent, \"div\");\n\n    // replace H1 with H2 as H1 should be only title that is displayed separately\n    this._replaceNodeTags(\n      this._getAllNodesWithTag(articleContent, [\"h1\"]),\n      \"h2\"\n    );\n\n    // Remove extra paragraphs\n    this._removeNodes(\n      this._getAllNodesWithTag(articleContent, [\"p\"]),\n      function (paragraph) {\n        // At this point, nasty iframes have been removed; only embedded video\n        // ones remain.\n        var contentElementCount = this._getAllNodesWithTag(paragraph, [\n          \"img\",\n          \"embed\",\n          \"object\",\n          \"iframe\",\n        ]).length;\n        return (\n          contentElementCount === 0 && !this._getInnerText(paragraph, false)\n        );\n      }\n    );\n\n    this._forEachNode(\n      this._getAllNodesWithTag(articleContent, [\"br\"]),\n      function (br) {\n        var next = this._nextNode(br.nextSibling);\n        if (next && next.tagName == \"P\") {\n          br.remove();\n        }\n      }\n    );\n\n    // Remove single-cell tables\n    this._forEachNode(\n      this._getAllNodesWithTag(articleContent, [\"table\"]),\n      function (table) {\n        var tbody = this._hasSingleTagInsideElement(table, \"TBODY\")\n          ? table.firstElementChild\n          : table;\n        if (this._hasSingleTagInsideElement(tbody, \"TR\")) {\n          var row = tbody.firstElementChild;\n          if (this._hasSingleTagInsideElement(row, \"TD\")) {\n            var cell = row.firstElementChild;\n            cell = this._setNodeTag(\n              cell,\n              this._everyNode(cell.childNodes, this._isPhrasingContent)\n                ? \"P\"\n                : \"DIV\"\n            );\n            table.parentNode.replaceChild(cell, table);\n          }\n        }\n      }\n    );\n  },\n\n  /**\n   * Initialize a node with the readability object. Also checks the\n   * className/id for special names to add to its score.\n   *\n   * @param Element\n   * @return void\n   **/\n  _initializeNode(node) {\n    node.readability = { contentScore: 0 };\n\n    switch (node.tagName) {\n      case \"DIV\":\n        node.readability.contentScore += 5;\n        break;\n\n      case \"PRE\":\n      case \"TD\":\n      case \"BLOCKQUOTE\":\n        node.readability.contentScore += 3;\n        break;\n\n      case \"ADDRESS\":\n      case \"OL\":\n      case \"UL\":\n      case \"DL\":\n      case \"DD\":\n      case \"DT\":\n      case \"LI\":\n      case \"FORM\":\n        node.readability.contentScore -= 3;\n        break;\n\n      case \"H1\":\n      case \"H2\":\n      case \"H3\":\n      case \"H4\":\n      case \"H5\":\n      case \"H6\":\n      case \"TH\":\n        node.readability.contentScore -= 5;\n        break;\n    }\n\n    node.readability.contentScore += this._getClassWeight(node);\n  },\n\n  _removeAndGetNext(node) {\n    var nextNode = this._getNextNode(node, true);\n    node.remove();\n    return nextNode;\n  },\n\n  /**\n   * Traverse the DOM from node to node, starting at the node passed in.\n   * Pass true for the second parameter to indicate this node itself\n   * (and its kids) are going away, and we want the next node over.\n   *\n   * Calling this in a loop will traverse the DOM depth-first.\n   *\n   * @param {Element} node\n   * @param {boolean} ignoreSelfAndKids\n   * @return {Element}\n   */\n  _getNextNode(node, ignoreSelfAndKids) {\n    // First check for kids if those aren't being ignored\n    if (!ignoreSelfAndKids && node.firstElementChild) {\n      return node.firstElementChild;\n    }\n    // Then for siblings...\n    if (node.nextElementSibling) {\n      return node.nextElementSibling;\n    }\n    // And finally, move up the parent chain *and* find a sibling\n    // (because this is depth-first traversal, we will have already\n    // seen the parent nodes themselves).\n    do {\n      node = node.parentNode;\n    } while (node && !node.nextElementSibling);\n    return node && node.nextElementSibling;\n  },\n\n  // compares second text to first one\n  // 1 = same text, 0 = completely different text\n  // works the way that it splits both texts into words and then finds words that are unique in second text\n  // the result is given by the lower length of unique parts\n  _textSimilarity(textA, textB) {\n    var tokensA = textA\n      .toLowerCase()\n      .split(this.REGEXPS.tokenize)\n      .filter(Boolean);\n    var tokensB = textB\n      .toLowerCase()\n      .split(this.REGEXPS.tokenize)\n      .filter(Boolean);\n    if (!tokensA.length || !tokensB.length) {\n      return 0;\n    }\n    var uniqTokensB = tokensB.filter(token => !tokensA.includes(token));\n    var distanceB = uniqTokensB.join(\" \").length / tokensB.join(\" \").length;\n    return 1 - distanceB;\n  },\n\n  /**\n   * Checks whether an element node contains a valid byline\n   *\n   * @param node {Element}\n   * @param matchString {string}\n   * @return boolean\n   */\n  _isValidByline(node, matchString) {\n    var rel = node.getAttribute(\"rel\");\n    var itemprop = node.getAttribute(\"itemprop\");\n    var bylineLength = node.textContent.trim().length;\n\n    return (\n      (rel === \"author\" ||\n        (itemprop && itemprop.includes(\"author\")) ||\n        this.REGEXPS.byline.test(matchString)) &&\n      !!bylineLength &&\n      bylineLength < 100\n    );\n  },\n\n  _getNodeAncestors(node, maxDepth) {\n    maxDepth = maxDepth || 0;\n    var i = 0,\n      ancestors = [];\n    while (node.parentNode) {\n      ancestors.push(node.parentNode);\n      if (maxDepth && ++i === maxDepth) {\n        break;\n      }\n      node = node.parentNode;\n    }\n    return ancestors;\n  },\n\n  /***\n   * grabArticle - Using a variety of metrics (content score, classname, element types), find the content that is\n   *         most likely to be the stuff a user wants to read. Then return it wrapped up in a div.\n   *\n   * @param page a document to run upon. Needs to be a full document, complete with body.\n   * @return Element\n   **/\n  /* eslint-disable-next-line complexity */\n  _grabArticle(page) {\n    this.log(\"**** grabArticle ****\");\n    var doc = this._doc;\n    var isPaging = page !== null;\n    page = page ? page : this._doc.body;\n\n    // We can't grab an article if we don't have a page!\n    if (!page) {\n      this.log(\"No body found in document. Abort.\");\n      return null;\n    }\n\n    var pageCacheHtml = page.innerHTML;\n\n    while (true) {\n      this.log(\"Starting grabArticle loop\");\n      var stripUnlikelyCandidates = this._flagIsActive(\n        this.FLAG_STRIP_UNLIKELYS\n      );\n\n      // First, node prepping. Trash nodes that look cruddy (like ones with the\n      // class name \"comment\", etc), and turn divs into P tags where they have been\n      // used inappropriately (as in, where they contain no other block level elements.)\n      var elementsToScore = [];\n      var node = this._doc.documentElement;\n\n      let shouldRemoveTitleHeader = true;\n\n      while (node) {\n        if (node.tagName === \"HTML\") {\n          this._articleLang = node.getAttribute(\"lang\");\n        }\n\n        var matchString = node.className + \" \" + node.id;\n\n        if (!this._isProbablyVisible(node)) {\n          this.log(\"Removing hidden node - \" + matchString);\n          node = this._removeAndGetNext(node);\n          continue;\n        }\n\n        // User is not able to see elements applied with both \"aria-modal = true\" and \"role = dialog\"\n        if (\n          node.getAttribute(\"aria-modal\") == \"true\" &&\n          node.getAttribute(\"role\") == \"dialog\"\n        ) {\n          node = this._removeAndGetNext(node);\n          continue;\n        }\n\n        // If we don't have a byline yet check to see if this node is a byline; if it is store the byline and remove the node.\n        if (\n          !this._articleByline &&\n          !this._metadata.byline &&\n          this._isValidByline(node, matchString)\n        ) {\n          // Find child node matching [itemprop=\"name\"] and use that if it exists for a more accurate author name byline\n          var endOfSearchMarkerNode = this._getNextNode(node, true);\n          var next = this._getNextNode(node);\n          var itemPropNameNode = null;\n          while (next && next != endOfSearchMarkerNode) {\n            var itemprop = next.getAttribute(\"itemprop\");\n            if (itemprop && itemprop.includes(\"name\")) {\n              itemPropNameNode = next;\n              break;\n            } else {\n              next = this._getNextNode(next);\n            }\n          }\n          this._articleByline = (itemPropNameNode ?? node).textContent.trim();\n          node = this._removeAndGetNext(node);\n          continue;\n        }\n\n        if (shouldRemoveTitleHeader && this._headerDuplicatesTitle(node)) {\n          this.log(\n            \"Removing header: \",\n            node.textContent.trim(),\n            this._articleTitle.trim()\n          );\n          shouldRemoveTitleHeader = false;\n          node = this._removeAndGetNext(node);\n          continue;\n        }\n\n        // Remove unlikely candidates\n        if (stripUnlikelyCandidates) {\n          if (\n            this.REGEXPS.unlikelyCandidates.test(matchString) &&\n            !this.REGEXPS.okMaybeItsACandidate.test(matchString) &&\n            !this._hasAncestorTag(node, \"table\") &&\n            !this._hasAncestorTag(node, \"code\") &&\n            node.tagName !== \"BODY\" &&\n            node.tagName !== \"A\"\n          ) {\n            this.log(\"Removing unlikely candidate - \" + matchString);\n            node = this._removeAndGetNext(node);\n            continue;\n          }\n\n          if (this.UNLIKELY_ROLES.includes(node.getAttribute(\"role\"))) {\n            this.log(\n              \"Removing content with role \" +\n                node.getAttribute(\"role\") +\n                \" - \" +\n                matchString\n            );\n            node = this._removeAndGetNext(node);\n            continue;\n          }\n        }\n\n        // Remove DIV, SECTION, and HEADER nodes without any content(e.g. text, image, video, or iframe).\n        if (\n          (node.tagName === \"DIV\" ||\n            node.tagName === \"SECTION\" ||\n            node.tagName === \"HEADER\" ||\n            node.tagName === \"H1\" ||\n            node.tagName === \"H2\" ||\n            node.tagName === \"H3\" ||\n            node.tagName === \"H4\" ||\n            node.tagName === \"H5\" ||\n            node.tagName === \"H6\") &&\n          this._isElementWithoutContent(node)\n        ) {\n          node = this._removeAndGetNext(node);\n          continue;\n        }\n\n        if (this.DEFAULT_TAGS_TO_SCORE.includes(node.tagName)) {\n          elementsToScore.push(node);\n        }\n\n        // Turn all divs that don't have children block level elements into p's\n        if (node.tagName === \"DIV\") {\n          // Put phrasing content into paragraphs.\n          var p = null;\n          var childNode = node.firstChild;\n          while (childNode) {\n            var nextSibling = childNode.nextSibling;\n            if (this._isPhrasingContent(childNode)) {\n              if (p !== null) {\n                p.appendChild(childNode);\n              } else if (!this._isWhitespace(childNode)) {\n                p = doc.createElement(\"p\");\n                node.replaceChild(p, childNode);\n                p.appendChild(childNode);\n              }\n            } else if (p !== null) {\n              while (p.lastChild && this._isWhitespace(p.lastChild)) {\n                p.lastChild.remove();\n              }\n              p = null;\n            }\n            childNode = nextSibling;\n          }\n\n          // Sites like http://mobile.slate.com encloses each paragraph with a DIV\n          // element. DIVs with only a P element inside and no text content can be\n          // safely converted into plain P elements to avoid confusing the scoring\n          // algorithm with DIVs with are, in practice, paragraphs.\n          if (\n            this._hasSingleTagInsideElement(node, \"P\") &&\n            this._getLinkDensity(node) < 0.25\n          ) {\n            var newNode = node.children[0];\n            node.parentNode.replaceChild(newNode, node);\n            node = newNode;\n            elementsToScore.push(node);\n          } else if (!this._hasChildBlockElement(node)) {\n            node = this._setNodeTag(node, \"P\");\n            elementsToScore.push(node);\n          }\n        }\n        node = this._getNextNode(node);\n      }\n\n      /**\n       * Loop through all paragraphs, and assign a score to them based on how content-y they look.\n       * Then add their score to their parent node.\n       *\n       * A score is determined by things like number of commas, class names, etc. Maybe eventually link density.\n       **/\n      var candidates = [];\n      this._forEachNode(elementsToScore, function (elementToScore) {\n        if (\n          !elementToScore.parentNode ||\n          typeof elementToScore.parentNode.tagName === \"undefined\"\n        ) {\n          return;\n        }\n\n        // If this paragraph is less than 25 characters, don't even count it.\n        var innerText = this._getInnerText(elementToScore);\n        if (innerText.length < 25) {\n          return;\n        }\n\n        // Exclude nodes with no ancestor.\n        var ancestors = this._getNodeAncestors(elementToScore, 5);\n        if (ancestors.length === 0) {\n          return;\n        }\n\n        var contentScore = 0;\n\n        // Add a point for the paragraph itself as a base.\n        contentScore += 1;\n\n        // Add points for any commas within this paragraph.\n        contentScore += innerText.split(this.REGEXPS.commas).length;\n\n        // For every 100 characters in this paragraph, add another point. Up to 3 points.\n        contentScore += Math.min(Math.floor(innerText.length / 100), 3);\n\n        // Initialize and score ancestors.\n        this._forEachNode(ancestors, function (ancestor, level) {\n          if (\n            !ancestor.tagName ||\n            !ancestor.parentNode ||\n            typeof ancestor.parentNode.tagName === \"undefined\"\n          ) {\n            return;\n          }\n\n          if (typeof ancestor.readability === \"undefined\") {\n            this._initializeNode(ancestor);\n            candidates.push(ancestor);\n          }\n\n          // Node score divider:\n          // - parent:             1 (no division)\n          // - grandparent:        2\n          // - great grandparent+: ancestor level * 3\n          if (level === 0) {\n            var scoreDivider = 1;\n          } else if (level === 1) {\n            scoreDivider = 2;\n          } else {\n            scoreDivider = level * 3;\n          }\n          ancestor.readability.contentScore += contentScore / scoreDivider;\n        });\n      });\n\n      // After we've calculated scores, loop through all of the possible\n      // candidate nodes we found and find the one with the highest score.\n      var topCandidates = [];\n      for (var c = 0, cl = candidates.length; c < cl; c += 1) {\n        var candidate = candidates[c];\n\n        // Scale the final candidates score based on link density. Good content\n        // should have a relatively small link density (5% or less) and be mostly\n        // unaffected by this operation.\n        var candidateScore =\n          candidate.readability.contentScore *\n          (1 - this._getLinkDensity(candidate));\n        candidate.readability.contentScore = candidateScore;\n\n        this.log(\"Candidate:\", candidate, \"with score \" + candidateScore);\n\n        for (var t = 0; t < this._nbTopCandidates; t++) {\n          var aTopCandidate = topCandidates[t];\n\n          if (\n            !aTopCandidate ||\n            candidateScore > aTopCandidate.readability.contentScore\n          ) {\n            topCandidates.splice(t, 0, candidate);\n            if (topCandidates.length > this._nbTopCandidates) {\n              topCandidates.pop();\n            }\n            break;\n          }\n        }\n      }\n\n      var topCandidate = topCandidates[0] || null;\n      var neededToCreateTopCandidate = false;\n      var parentOfTopCandidate;\n\n      // If we still have no top candidate, just use the body as a last resort.\n      // We also have to copy the body node so it is something we can modify.\n      if (topCandidate === null || topCandidate.tagName === \"BODY\") {\n        // Move all of the page's children into topCandidate\n        topCandidate = doc.createElement(\"DIV\");\n        neededToCreateTopCandidate = true;\n        // Move everything (not just elements, also text nodes etc.) into the container\n        // so we even include text directly in the body:\n        while (page.firstChild) {\n          this.log(\"Moving child out:\", page.firstChild);\n          topCandidate.appendChild(page.firstChild);\n        }\n\n        page.appendChild(topCandidate);\n\n        this._initializeNode(topCandidate);\n      } else if (topCandidate) {\n        // Find a better top candidate node if it contains (at least three) nodes which belong to `topCandidates` array\n        // and whose scores are quite closed with current `topCandidate` node.\n        var alternativeCandidateAncestors = [];\n        for (var i = 1; i < topCandidates.length; i++) {\n          if (\n            topCandidates[i].readability.contentScore /\n              topCandidate.readability.contentScore >=\n            0.75\n          ) {\n            alternativeCandidateAncestors.push(\n              this._getNodeAncestors(topCandidates[i])\n            );\n          }\n        }\n        var MINIMUM_TOPCANDIDATES = 3;\n        if (alternativeCandidateAncestors.length >= MINIMUM_TOPCANDIDATES) {\n          parentOfTopCandidate = topCandidate.parentNode;\n          while (parentOfTopCandidate.tagName !== \"BODY\") {\n            var listsContainingThisAncestor = 0;\n            for (\n              var ancestorIndex = 0;\n              ancestorIndex < alternativeCandidateAncestors.length &&\n              listsContainingThisAncestor < MINIMUM_TOPCANDIDATES;\n              ancestorIndex++\n            ) {\n              listsContainingThisAncestor += Number(\n                alternativeCandidateAncestors[ancestorIndex].includes(\n                  parentOfTopCandidate\n                )\n              );\n            }\n            if (listsContainingThisAncestor >= MINIMUM_TOPCANDIDATES) {\n              topCandidate = parentOfTopCandidate;\n              break;\n            }\n            parentOfTopCandidate = parentOfTopCandidate.parentNode;\n          }\n        }\n        if (!topCandidate.readability) {\n          this._initializeNode(topCandidate);\n        }\n\n        // Because of our bonus system, parents of candidates might have scores\n        // themselves. They get half of the node. There won't be nodes with higher\n        // scores than our topCandidate, but if we see the score going *up* in the first\n        // few steps up the tree, that's a decent sign that there might be more content\n        // lurking in other places that we want to unify in. The sibling stuff\n        // below does some of that - but only if we've looked high enough up the DOM\n        // tree.\n        parentOfTopCandidate = topCandidate.parentNode;\n        var lastScore = topCandidate.readability.contentScore;\n        // The scores shouldn't get too low.\n        var scoreThreshold = lastScore / 3;\n        while (parentOfTopCandidate.tagName !== \"BODY\") {\n          if (!parentOfTopCandidate.readability) {\n            parentOfTopCandidate = parentOfTopCandidate.parentNode;\n            continue;\n          }\n          var parentScore = parentOfTopCandidate.readability.contentScore;\n          if (parentScore < scoreThreshold) {\n            break;\n          }\n          if (parentScore > lastScore) {\n            // Alright! We found a better parent to use.\n            topCandidate = parentOfTopCandidate;\n            break;\n          }\n          lastScore = parentOfTopCandidate.readability.contentScore;\n          parentOfTopCandidate = parentOfTopCandidate.parentNode;\n        }\n\n        // If the top candidate is the only child, use parent instead. This will help sibling\n        // joining logic when adjacent content is actually located in parent's sibling node.\n        parentOfTopCandidate = topCandidate.parentNode;\n        while (\n          parentOfTopCandidate.tagName != \"BODY\" &&\n          parentOfTopCandidate.children.length == 1\n        ) {\n          topCandidate = parentOfTopCandidate;\n          parentOfTopCandidate = topCandidate.parentNode;\n        }\n        if (!topCandidate.readability) {\n          this._initializeNode(topCandidate);\n        }\n      }\n\n      // Now that we have the top candidate, look through its siblings for content\n      // that might also be related. Things like preambles, content split by ads\n      // that we removed, etc.\n      var articleContent = doc.createElement(\"DIV\");\n      if (isPaging) {\n        articleContent.id = \"readability-content\";\n      }\n\n      var siblingScoreThreshold = Math.max(\n        10,\n        topCandidate.readability.contentScore * 0.2\n      );\n      // Keep potential top candidate's parent node to try to get text direction of it later.\n      parentOfTopCandidate = topCandidate.parentNode;\n      var siblings = parentOfTopCandidate.children;\n\n      for (var s = 0, sl = siblings.length; s < sl; s++) {\n        var sibling = siblings[s];\n        var append = false;\n\n        this.log(\n          \"Looking at sibling node:\",\n          sibling,\n          sibling.readability\n            ? \"with score \" + sibling.readability.contentScore\n            : \"\"\n        );\n        this.log(\n          \"Sibling has score\",\n          sibling.readability ? sibling.readability.contentScore : \"Unknown\"\n        );\n\n        if (sibling === topCandidate) {\n          append = true;\n        } else {\n          var contentBonus = 0;\n\n          // Give a bonus if sibling nodes and top candidates have the example same classname\n          if (\n            sibling.className === topCandidate.className &&\n            topCandidate.className !== \"\"\n          ) {\n            contentBonus += topCandidate.readability.contentScore * 0.2;\n          }\n\n          if (\n            sibling.readability &&\n            sibling.readability.contentScore + contentBonus >=\n              siblingScoreThreshold\n          ) {\n            append = true;\n          } else if (sibling.nodeName === \"P\") {\n            var linkDensity = this._getLinkDensity(sibling);\n            var nodeContent = this._getInnerText(sibling);\n            var nodeLength = nodeContent.length;\n\n            if (nodeLength > 80 && linkDensity < 0.25) {\n              append = true;\n            } else if (\n              nodeLength < 80 &&\n              nodeLength > 0 &&\n              linkDensity === 0 &&\n              nodeContent.search(/\\.( |$)/) !== -1\n            ) {\n              append = true;\n            }\n          }\n        }\n\n        if (append) {\n          this.log(\"Appending node:\", sibling);\n\n          if (!this.ALTER_TO_DIV_EXCEPTIONS.includes(sibling.nodeName)) {\n            // We have a node that isn't a common block level element, like a form or td tag.\n            // Turn it into a div so it doesn't get filtered out later by accident.\n            this.log(\"Altering sibling:\", sibling, \"to div.\");\n\n            sibling = this._setNodeTag(sibling, \"DIV\");\n          }\n\n          articleContent.appendChild(sibling);\n          // Fetch children again to make it compatible\n          // with DOM parsers without live collection support.\n          siblings = parentOfTopCandidate.children;\n          // siblings is a reference to the children array, and\n          // sibling is removed from the array when we call appendChild().\n          // As a result, we must revisit this index since the nodes\n          // have been shifted.\n          s -= 1;\n          sl -= 1;\n        }\n      }\n\n      if (this._debug) {\n        this.log(\"Article content pre-prep: \" + articleContent.innerHTML);\n      }\n      // So we have all of the content that we need. Now we clean it up for presentation.\n      this._prepArticle(articleContent);\n      if (this._debug) {\n        this.log(\"Article content post-prep: \" + articleContent.innerHTML);\n      }\n\n      if (neededToCreateTopCandidate) {\n        // We already created a fake div thing, and there wouldn't have been any siblings left\n        // for the previous loop, so there's no point trying to create a new div, and then\n        // move all the children over. Just assign IDs and class names here. No need to append\n        // because that already happened anyway.\n        topCandidate.id = \"readability-page-1\";\n        topCandidate.className = \"page\";\n      } else {\n        var div = doc.createElement(\"DIV\");\n        div.id = \"readability-page-1\";\n        div.className = \"page\";\n        while (articleContent.firstChild) {\n          div.appendChild(articleContent.firstChild);\n        }\n        articleContent.appendChild(div);\n      }\n\n      if (this._debug) {\n        this.log(\"Article content after paging: \" + articleContent.innerHTML);\n      }\n\n      var parseSuccessful = true;\n\n      // Now that we've gone through the full algorithm, check to see if\n      // we got any meaningful content. If we didn't, we may need to re-run\n      // grabArticle with different flags set. This gives us a higher likelihood of\n      // finding the content, and the sieve approach gives us a higher likelihood of\n      // finding the -right- content.\n      var textLength = this._getInnerText(articleContent, true).length;\n      if (textLength < this._charThreshold) {\n        parseSuccessful = false;\n        // eslint-disable-next-line no-unsanitized/property\n        page.innerHTML = pageCacheHtml;\n\n        this._attempts.push({\n          articleContent,\n          textLength,\n        });\n\n        if (this._flagIsActive(this.FLAG_STRIP_UNLIKELYS)) {\n          this._removeFlag(this.FLAG_STRIP_UNLIKELYS);\n        } else if (this._flagIsActive(this.FLAG_WEIGHT_CLASSES)) {\n          this._removeFlag(this.FLAG_WEIGHT_CLASSES);\n        } else if (this._flagIsActive(this.FLAG_CLEAN_CONDITIONALLY)) {\n          this._removeFlag(this.FLAG_CLEAN_CONDITIONALLY);\n        } else {\n          // No luck after removing flags, just return the longest text we found during the different loops\n          this._attempts.sort(function (a, b) {\n            return b.textLength - a.textLength;\n          });\n\n          // But first check if we actually have something\n          if (!this._attempts[0].textLength) {\n            return null;\n          }\n\n          articleContent = this._attempts[0].articleContent;\n          parseSuccessful = true;\n        }\n      }\n\n      if (parseSuccessful) {\n        // Find out text direction from ancestors of final top candidate.\n        var ancestors = [parentOfTopCandidate, topCandidate].concat(\n          this._getNodeAncestors(parentOfTopCandidate)\n        );\n        this._someNode(ancestors, function (ancestor) {\n          if (!ancestor.tagName) {\n            return false;\n          }\n          var articleDir = ancestor.getAttribute(\"dir\");\n          if (articleDir) {\n            this._articleDir = articleDir;\n            return true;\n          }\n          return false;\n        });\n        return articleContent;\n      }\n    }\n  },\n\n  /**\n   * Converts some of the common HTML entities in string to their corresponding characters.\n   *\n   * @param str {string} - a string to unescape.\n   * @return string without HTML entity.\n   */\n  _unescapeHtmlEntities(str) {\n    if (!str) {\n      return str;\n    }\n\n    var htmlEscapeMap = this.HTML_ESCAPE_MAP;\n    return str\n      .replace(/&(quot|amp|apos|lt|gt);/g, function (_, tag) {\n        return htmlEscapeMap[tag];\n      })\n      .replace(/&#(?:x([0-9a-f]+)|([0-9]+));/gi, function (_, hex, numStr) {\n        var num = parseInt(hex || numStr, hex ? 16 : 10);\n\n        // these character references are replaced by a conforming HTML parser\n        if (num == 0 || num > 0x10ffff || (num >= 0xd800 && num <= 0xdfff)) {\n          num = 0xfffd;\n        }\n\n        return String.fromCodePoint(num);\n      });\n  },\n\n  /**\n   * Try to extract metadata from JSON-LD object.\n   * For now, only Schema.org objects of type Article or its subtypes are supported.\n   * @return Object with any metadata that could be extracted (possibly none)\n   */\n  _getJSONLD(doc) {\n    var scripts = this._getAllNodesWithTag(doc, [\"script\"]);\n\n    var metadata;\n\n    this._forEachNode(scripts, function (jsonLdElement) {\n      if (\n        !metadata &&\n        jsonLdElement.getAttribute(\"type\") === \"application/ld+json\"\n      ) {\n        try {\n          // Strip CDATA markers if present\n          var content = jsonLdElement.textContent.replace(\n            /^\\s*<!\\[CDATA\\[|\\]\\]>\\s*$/g,\n            \"\"\n          );\n          var parsed = JSON.parse(content);\n\n          if (Array.isArray(parsed)) {\n            parsed = parsed.find(it => {\n              return (\n                it[\"@type\"] &&\n                it[\"@type\"].match(this.REGEXPS.jsonLdArticleTypes)\n              );\n            });\n            if (!parsed) {\n              return;\n            }\n          }\n\n          var schemaDotOrgRegex = /^https?\\:\\/\\/schema\\.org\\/?$/;\n          var matches =\n            (typeof parsed[\"@context\"] === \"string\" &&\n              parsed[\"@context\"].match(schemaDotOrgRegex)) ||\n            (typeof parsed[\"@context\"] === \"object\" &&\n              typeof parsed[\"@context\"][\"@vocab\"] == \"string\" &&\n              parsed[\"@context\"][\"@vocab\"].match(schemaDotOrgRegex));\n\n          if (!matches) {\n            return;\n          }\n\n          if (!parsed[\"@type\"] && Array.isArray(parsed[\"@graph\"])) {\n            parsed = parsed[\"@graph\"].find(it => {\n              return (it[\"@type\"] || \"\").match(this.REGEXPS.jsonLdArticleTypes);\n            });\n          }\n\n          if (\n            !parsed ||\n            !parsed[\"@type\"] ||\n            !parsed[\"@type\"].match(this.REGEXPS.jsonLdArticleTypes)\n          ) {\n            return;\n          }\n\n          metadata = {};\n\n          if (\n            typeof parsed.name === \"string\" &&\n            typeof parsed.headline === \"string\" &&\n            parsed.name !== parsed.headline\n          ) {\n            // we have both name and headline element in the JSON-LD. They should both be the same but some websites like aktualne.cz\n            // put their own name into \"name\" and the article title to \"headline\" which confuses Readability. So we try to check if either\n            // \"name\" or \"headline\" closely matches the html title, and if so, use that one. If not, then we use \"name\" by default.\n\n            var title = this._getArticleTitle();\n            var nameMatches = this._textSimilarity(parsed.name, title) > 0.75;\n            var headlineMatches =\n              this._textSimilarity(parsed.headline, title) > 0.75;\n\n            if (headlineMatches && !nameMatches) {\n              metadata.title = parsed.headline;\n            } else {\n              metadata.title = parsed.name;\n            }\n          } else if (typeof parsed.name === \"string\") {\n            metadata.title = parsed.name.trim();\n          } else if (typeof parsed.headline === \"string\") {\n            metadata.title = parsed.headline.trim();\n          }\n          if (parsed.author) {\n            if (typeof parsed.author.name === \"string\") {\n              metadata.byline = parsed.author.name.trim();\n            } else if (\n              Array.isArray(parsed.author) &&\n              parsed.author[0] &&\n              typeof parsed.author[0].name === \"string\"\n            ) {\n              metadata.byline = parsed.author\n                .filter(function (author) {\n                  return author && typeof author.name === \"string\";\n                })\n                .map(function (author) {\n                  return author.name.trim();\n                })\n                .join(\", \");\n            }\n          }\n          if (typeof parsed.description === \"string\") {\n            metadata.excerpt = parsed.description.trim();\n          }\n          if (parsed.publisher && typeof parsed.publisher.name === \"string\") {\n            metadata.siteName = parsed.publisher.name.trim();\n          }\n          if (typeof parsed.datePublished === \"string\") {\n            metadata.datePublished = parsed.datePublished.trim();\n          }\n        } catch (err) {\n          this.log(err.message);\n        }\n      }\n    });\n    return metadata ? metadata : {};\n  },\n\n  /**\n   * Attempts to get excerpt and byline metadata for the article.\n   *\n   * @param {Object} jsonld — object containing any metadata that\n   * could be extracted from JSON-LD object.\n   *\n   * @return Object with optional \"excerpt\" and \"byline\" properties\n   */\n  _getArticleMetadata(jsonld) {\n    var metadata = {};\n    var values = {};\n    var metaElements = this._doc.getElementsByTagName(\"meta\");\n\n    // property is a space-separated list of values\n    var propertyPattern =\n      /\\s*(article|dc|dcterm|og|twitter)\\s*:\\s*(author|creator|description|published_time|title|site_name)\\s*/gi;\n\n    // name is a single value\n    var namePattern =\n      /^\\s*(?:(dc|dcterm|og|twitter|parsely|weibo:(article|webpage))\\s*[-\\.:]\\s*)?(author|creator|pub-date|description|title|site_name)\\s*$/i;\n\n    // Find description tags.\n    this._forEachNode(metaElements, function (element) {\n      var elementName = element.getAttribute(\"name\");\n      var elementProperty = element.getAttribute(\"property\");\n      var content = element.getAttribute(\"content\");\n      if (!content) {\n        return;\n      }\n      var matches = null;\n      var name = null;\n\n      if (elementProperty) {\n        matches = elementProperty.match(propertyPattern);\n        if (matches) {\n          // Convert to lowercase, and remove any whitespace\n          // so we can match below.\n          name = matches[0].toLowerCase().replace(/\\s/g, \"\");\n          // multiple authors\n          values[name] = content.trim();\n        }\n      }\n      if (!matches && elementName && namePattern.test(elementName)) {\n        name = elementName;\n        if (content) {\n          // Convert to lowercase, remove any whitespace, and convert dots\n          // to colons so we can match below.\n          name = name.toLowerCase().replace(/\\s/g, \"\").replace(/\\./g, \":\");\n          values[name] = content.trim();\n        }\n      }\n    });\n\n    // get title\n    metadata.title =\n      jsonld.title ||\n      values[\"dc:title\"] ||\n      values[\"dcterm:title\"] ||\n      values[\"og:title\"] ||\n      values[\"weibo:article:title\"] ||\n      values[\"weibo:webpage:title\"] ||\n      values.title ||\n      values[\"twitter:title\"] ||\n      values[\"parsely-title\"];\n\n    if (!metadata.title) {\n      metadata.title = this._getArticleTitle();\n    }\n\n    const articleAuthor =\n      typeof values[\"article:author\"] === \"string\" &&\n      !this._isUrl(values[\"article:author\"])\n        ? values[\"article:author\"]\n        : undefined;\n\n    // get author\n    metadata.byline =\n      jsonld.byline ||\n      values[\"dc:creator\"] ||\n      values[\"dcterm:creator\"] ||\n      values.author ||\n      values[\"parsely-author\"] ||\n      articleAuthor;\n\n    // get description\n    metadata.excerpt =\n      jsonld.excerpt ||\n      values[\"dc:description\"] ||\n      values[\"dcterm:description\"] ||\n      values[\"og:description\"] ||\n      values[\"weibo:article:description\"] ||\n      values[\"weibo:webpage:description\"] ||\n      values.description ||\n      values[\"twitter:description\"];\n\n    // get site name\n    metadata.siteName = jsonld.siteName || values[\"og:site_name\"];\n\n    // get article published time\n    metadata.publishedTime =\n      jsonld.datePublished ||\n      values[\"article:published_time\"] ||\n      values[\"parsely-pub-date\"] ||\n      null;\n\n    // in many sites the meta value is escaped with HTML entities,\n    // so here we need to unescape it\n    metadata.title = this._unescapeHtmlEntities(metadata.title);\n    metadata.byline = this._unescapeHtmlEntities(metadata.byline);\n    metadata.excerpt = this._unescapeHtmlEntities(metadata.excerpt);\n    metadata.siteName = this._unescapeHtmlEntities(metadata.siteName);\n    metadata.publishedTime = this._unescapeHtmlEntities(metadata.publishedTime);\n\n    return metadata;\n  },\n\n  /**\n   * Check if node is image, or if node contains exactly only one image\n   * whether as a direct child or as its descendants.\n   *\n   * @param Element\n   **/\n  _isSingleImage(node) {\n    while (node) {\n      if (node.tagName === \"IMG\") {\n        return true;\n      }\n      if (node.children.length !== 1 || node.textContent.trim() !== \"\") {\n        return false;\n      }\n      node = node.children[0];\n    }\n    return false;\n  },\n\n  /**\n   * Find all <noscript> that are located after <img> nodes, and which contain only one\n   * <img> element. Replace the first image with the image from inside the <noscript> tag,\n   * and remove the <noscript> tag. This improves the quality of the images we use on\n   * some sites (e.g. Medium).\n   *\n   * @param Element\n   **/\n  _unwrapNoscriptImages(doc) {\n    // Find img without source or attributes that might contains image, and remove it.\n    // This is done to prevent a placeholder img is replaced by img from noscript in next step.\n    var imgs = Array.from(doc.getElementsByTagName(\"img\"));\n    this._forEachNode(imgs, function (img) {\n      for (var i = 0; i < img.attributes.length; i++) {\n        var attr = img.attributes[i];\n        switch (attr.name) {\n          case \"src\":\n          case \"srcset\":\n          case \"data-src\":\n          case \"data-srcset\":\n            return;\n        }\n\n        if (/\\.(jpg|jpeg|png|webp)/i.test(attr.value)) {\n          return;\n        }\n      }\n\n      img.remove();\n    });\n\n    // Next find noscript and try to extract its image\n    var noscripts = Array.from(doc.getElementsByTagName(\"noscript\"));\n    this._forEachNode(noscripts, function (noscript) {\n      // Parse content of noscript and make sure it only contains image\n      if (!this._isSingleImage(noscript)) {\n        return;\n      }\n      var tmp = doc.createElement(\"div\");\n      // We're running in the document context, and using unmodified\n      // document contents, so doing this should be safe.\n      // (Also we heavily discourage people from allowing script to\n      // run at all in this document...)\n      // eslint-disable-next-line no-unsanitized/property\n      tmp.innerHTML = noscript.innerHTML;\n\n      // If noscript has previous sibling and it only contains image,\n      // replace it with noscript content. However we also keep old\n      // attributes that might contains image.\n      var prevElement = noscript.previousElementSibling;\n      if (prevElement && this._isSingleImage(prevElement)) {\n        var prevImg = prevElement;\n        if (prevImg.tagName !== \"IMG\") {\n          prevImg = prevElement.getElementsByTagName(\"img\")[0];\n        }\n\n        var newImg = tmp.getElementsByTagName(\"img\")[0];\n        for (var i = 0; i < prevImg.attributes.length; i++) {\n          var attr = prevImg.attributes[i];\n          if (attr.value === \"\") {\n            continue;\n          }\n\n          if (\n            attr.name === \"src\" ||\n            attr.name === \"srcset\" ||\n            /\\.(jpg|jpeg|png|webp)/i.test(attr.value)\n          ) {\n            if (newImg.getAttribute(attr.name) === attr.value) {\n              continue;\n            }\n\n            var attrName = attr.name;\n            if (newImg.hasAttribute(attrName)) {\n              attrName = \"data-old-\" + attrName;\n            }\n\n            newImg.setAttribute(attrName, attr.value);\n          }\n        }\n\n        noscript.parentNode.replaceChild(tmp.firstElementChild, prevElement);\n      }\n    });\n  },\n\n  /**\n   * Removes script tags from the document.\n   *\n   * @param Element\n   **/\n  _removeScripts(doc) {\n    this._removeNodes(this._getAllNodesWithTag(doc, [\"script\", \"noscript\"]));\n  },\n\n  /**\n   * Check if this node has only whitespace and a single element with given tag\n   * Returns false if the DIV node contains non-empty text nodes\n   * or if it contains no element with given tag or more than 1 element.\n   *\n   * @param Element\n   * @param string tag of child element\n   **/\n  _hasSingleTagInsideElement(element, tag) {\n    // There should be exactly 1 element child with given tag\n    if (element.children.length != 1 || element.children[0].tagName !== tag) {\n      return false;\n    }\n\n    // And there should be no text nodes with real content\n    return !this._someNode(element.childNodes, function (node) {\n      return (\n        node.nodeType === this.TEXT_NODE &&\n        this.REGEXPS.hasContent.test(node.textContent)\n      );\n    });\n  },\n\n  _isElementWithoutContent(node) {\n    return (\n      node.nodeType === this.ELEMENT_NODE &&\n      !node.textContent.trim().length &&\n      (!node.children.length ||\n        node.children.length ==\n          node.getElementsByTagName(\"br\").length +\n            node.getElementsByTagName(\"hr\").length)\n    );\n  },\n\n  /**\n   * Determine whether element has any children block level elements.\n   *\n   * @param Element\n   */\n  _hasChildBlockElement(element) {\n    return this._someNode(element.childNodes, function (node) {\n      return (\n        this.DIV_TO_P_ELEMS.has(node.tagName) ||\n        this._hasChildBlockElement(node)\n      );\n    });\n  },\n\n  /***\n   * Determine if a node qualifies as phrasing content.\n   * https://developer.mozilla.org/en-US/docs/Web/Guide/HTML/Content_categories#Phrasing_content\n   **/\n  _isPhrasingContent(node) {\n    return (\n      node.nodeType === this.TEXT_NODE ||\n      this.PHRASING_ELEMS.includes(node.tagName) ||\n      ((node.tagName === \"A\" ||\n        node.tagName === \"DEL\" ||\n        node.tagName === \"INS\") &&\n        this._everyNode(node.childNodes, this._isPhrasingContent))\n    );\n  },\n\n  _isWhitespace(node) {\n    return (\n      (node.nodeType === this.TEXT_NODE &&\n        node.textContent.trim().length === 0) ||\n      (node.nodeType === this.ELEMENT_NODE && node.tagName === \"BR\")\n    );\n  },\n\n  /**\n   * Get the inner text of a node - cross browser compatibly.\n   * This also strips out any excess whitespace to be found.\n   *\n   * @param Element\n   * @param Boolean normalizeSpaces (default: true)\n   * @return string\n   **/\n  _getInnerText(e, normalizeSpaces) {\n    normalizeSpaces =\n      typeof normalizeSpaces === \"undefined\" ? true : normalizeSpaces;\n    var textContent = e.textContent.trim();\n\n    if (normalizeSpaces) {\n      return textContent.replace(this.REGEXPS.normalize, \" \");\n    }\n    return textContent;\n  },\n\n  /**\n   * Get the number of times a string s appears in the node e.\n   *\n   * @param Element\n   * @param string - what to split on. Default is \",\"\n   * @return number (integer)\n   **/\n  _getCharCount(e, s) {\n    s = s || \",\";\n    return this._getInnerText(e).split(s).length - 1;\n  },\n\n  /**\n   * Remove the style attribute on every e and under.\n   * TODO: Test if getElementsByTagName(*) is faster.\n   *\n   * @param Element\n   * @return void\n   **/\n  _cleanStyles(e) {\n    if (!e || e.tagName.toLowerCase() === \"svg\") {\n      return;\n    }\n\n    // Remove `style` and deprecated presentational attributes\n    for (var i = 0; i < this.PRESENTATIONAL_ATTRIBUTES.length; i++) {\n      e.removeAttribute(this.PRESENTATIONAL_ATTRIBUTES[i]);\n    }\n\n    if (this.DEPRECATED_SIZE_ATTRIBUTE_ELEMS.includes(e.tagName)) {\n      e.removeAttribute(\"width\");\n      e.removeAttribute(\"height\");\n    }\n\n    var cur = e.firstElementChild;\n    while (cur !== null) {\n      this._cleanStyles(cur);\n      cur = cur.nextElementSibling;\n    }\n  },\n\n  /**\n   * Get the density of links as a percentage of the content\n   * This is the amount of text that is inside a link divided by the total text in the node.\n   *\n   * @param Element\n   * @return number (float)\n   **/\n  _getLinkDensity(element) {\n    var textLength = this._getInnerText(element).length;\n    if (textLength === 0) {\n      return 0;\n    }\n\n    var linkLength = 0;\n\n    // XXX implement _reduceNodeList?\n    this._forEachNode(element.getElementsByTagName(\"a\"), function (linkNode) {\n      var href = linkNode.getAttribute(\"href\");\n      var coefficient = href && this.REGEXPS.hashUrl.test(href) ? 0.3 : 1;\n      linkLength += this._getInnerText(linkNode).length * coefficient;\n    });\n\n    return linkLength / textLength;\n  },\n\n  /**\n   * Get an elements class/id weight. Uses regular expressions to tell if this\n   * element looks good or bad.\n   *\n   * @param Element\n   * @return number (Integer)\n   **/\n  _getClassWeight(e) {\n    if (!this._flagIsActive(this.FLAG_WEIGHT_CLASSES)) {\n      return 0;\n    }\n\n    var weight = 0;\n\n    // Look for a special classname\n    if (typeof e.className === \"string\" && e.className !== \"\") {\n      if (this.REGEXPS.negative.test(e.className)) {\n        weight -= 25;\n      }\n\n      if (this.REGEXPS.positive.test(e.className)) {\n        weight += 25;\n      }\n    }\n\n    // Look for a special ID\n    if (typeof e.id === \"string\" && e.id !== \"\") {\n      if (this.REGEXPS.negative.test(e.id)) {\n        weight -= 25;\n      }\n\n      if (this.REGEXPS.positive.test(e.id)) {\n        weight += 25;\n      }\n    }\n\n    return weight;\n  },\n\n  /**\n   * Clean a node of all elements of type \"tag\".\n   * (Unless it's a youtube/vimeo video. People love movies.)\n   *\n   * @param Element\n   * @param string tag to clean\n   * @return void\n   **/\n  _clean(e, tag) {\n    var isEmbed = [\"object\", \"embed\", \"iframe\"].includes(tag);\n\n    this._removeNodes(this._getAllNodesWithTag(e, [tag]), function (element) {\n      // Allow youtube and vimeo videos through as people usually want to see those.\n      if (isEmbed) {\n        // First, check the elements attributes to see if any of them contain youtube or vimeo\n        for (var i = 0; i < element.attributes.length; i++) {\n          if (this._allowedVideoRegex.test(element.attributes[i].value)) {\n            return false;\n          }\n        }\n\n        // For embed with <object> tag, check inner HTML as well.\n        if (\n          element.tagName === \"object\" &&\n          this._allowedVideoRegex.test(element.innerHTML)\n        ) {\n          return false;\n        }\n      }\n\n      return true;\n    });\n  },\n\n  /**\n   * Check if a given node has one of its ancestor tag name matching the\n   * provided one.\n   * @param  HTMLElement node\n   * @param  String      tagName\n   * @param  Number      maxDepth\n   * @param  Function    filterFn a filter to invoke to determine whether this node 'counts'\n   * @return Boolean\n   */\n  _hasAncestorTag(node, tagName, maxDepth, filterFn) {\n    maxDepth = maxDepth || 3;\n    tagName = tagName.toUpperCase();\n    var depth = 0;\n    while (node.parentNode) {\n      if (maxDepth > 0 && depth > maxDepth) {\n        return false;\n      }\n      if (\n        node.parentNode.tagName === tagName &&\n        (!filterFn || filterFn(node.parentNode))\n      ) {\n        return true;\n      }\n      node = node.parentNode;\n      depth++;\n    }\n    return false;\n  },\n\n  /**\n   * Return an object indicating how many rows and columns this table has.\n   */\n  _getRowAndColumnCount(table) {\n    var rows = 0;\n    var columns = 0;\n    var trs = table.getElementsByTagName(\"tr\");\n    for (var i = 0; i < trs.length; i++) {\n      var rowspan = trs[i].getAttribute(\"rowspan\") || 0;\n      if (rowspan) {\n        rowspan = parseInt(rowspan, 10);\n      }\n      rows += rowspan || 1;\n\n      // Now look for column-related info\n      var columnsInThisRow = 0;\n      var cells = trs[i].getElementsByTagName(\"td\");\n      for (var j = 0; j < cells.length; j++) {\n        var colspan = cells[j].getAttribute(\"colspan\") || 0;\n        if (colspan) {\n          colspan = parseInt(colspan, 10);\n        }\n        columnsInThisRow += colspan || 1;\n      }\n      columns = Math.max(columns, columnsInThisRow);\n    }\n    return { rows, columns };\n  },\n\n  /**\n   * Look for 'data' (as opposed to 'layout') tables, for which we use\n   * similar checks as\n   * https://searchfox.org/mozilla-central/rev/f82d5c549f046cb64ce5602bfd894b7ae807c8f8/accessible/generic/TableAccessible.cpp#19\n   */\n  _markDataTables(root) {\n    var tables = root.getElementsByTagName(\"table\");\n    for (var i = 0; i < tables.length; i++) {\n      var table = tables[i];\n      var role = table.getAttribute(\"role\");\n      if (role == \"presentation\") {\n        table._readabilityDataTable = false;\n        continue;\n      }\n      var datatable = table.getAttribute(\"datatable\");\n      if (datatable == \"0\") {\n        table._readabilityDataTable = false;\n        continue;\n      }\n      var summary = table.getAttribute(\"summary\");\n      if (summary) {\n        table._readabilityDataTable = true;\n        continue;\n      }\n\n      var caption = table.getElementsByTagName(\"caption\")[0];\n      if (caption && caption.childNodes.length) {\n        table._readabilityDataTable = true;\n        continue;\n      }\n\n      // If the table has a descendant with any of these tags, consider a data table:\n      var dataTableDescendants = [\"col\", \"colgroup\", \"tfoot\", \"thead\", \"th\"];\n      var descendantExists = function (tag) {\n        return !!table.getElementsByTagName(tag)[0];\n      };\n      if (dataTableDescendants.some(descendantExists)) {\n        this.log(\"Data table because found data-y descendant\");\n        table._readabilityDataTable = true;\n        continue;\n      }\n\n      // Nested tables indicate a layout table:\n      if (table.getElementsByTagName(\"table\")[0]) {\n        table._readabilityDataTable = false;\n        continue;\n      }\n\n      var sizeInfo = this._getRowAndColumnCount(table);\n\n      if (sizeInfo.columns == 1 || sizeInfo.rows == 1) {\n        // single colum/row tables are commonly used for page layout purposes.\n        table._readabilityDataTable = false;\n        continue;\n      }\n\n      if (sizeInfo.rows >= 10 || sizeInfo.columns > 4) {\n        table._readabilityDataTable = true;\n        continue;\n      }\n      // Now just go by size entirely:\n      table._readabilityDataTable = sizeInfo.rows * sizeInfo.columns > 10;\n    }\n  },\n\n  /* convert images and figures that have properties like data-src into images that can be loaded without JS */\n  _fixLazyImages(root) {\n    this._forEachNode(\n      this._getAllNodesWithTag(root, [\"img\", \"picture\", \"figure\"]),\n      function (elem) {\n        // In some sites (e.g. Kotaku), they put 1px square image as base64 data uri in the src attribute.\n        // So, here we check if the data uri is too short, just might as well remove it.\n        if (elem.src && this.REGEXPS.b64DataUrl.test(elem.src)) {\n          // Make sure it's not SVG, because SVG can have a meaningful image in under 133 bytes.\n          var parts = this.REGEXPS.b64DataUrl.exec(elem.src);\n          if (parts[1] === \"image/svg+xml\") {\n            return;\n          }\n\n          // Make sure this element has other attributes which contains image.\n          // If it doesn't, then this src is important and shouldn't be removed.\n          var srcCouldBeRemoved = false;\n          for (var i = 0; i < elem.attributes.length; i++) {\n            var attr = elem.attributes[i];\n            if (attr.name === \"src\") {\n              continue;\n            }\n\n            if (/\\.(jpg|jpeg|png|webp)/i.test(attr.value)) {\n              srcCouldBeRemoved = true;\n              break;\n            }\n          }\n\n          // Here we assume if image is less than 100 bytes (or 133 after encoded to base64)\n          // it will be too small, therefore it might be placeholder image.\n          if (srcCouldBeRemoved) {\n            var b64starts = parts[0].length;\n            var b64length = elem.src.length - b64starts;\n            if (b64length < 133) {\n              elem.removeAttribute(\"src\");\n            }\n          }\n        }\n\n        // also check for \"null\" to work around https://github.com/jsdom/jsdom/issues/2580\n        if (\n          (elem.src || (elem.srcset && elem.srcset != \"null\")) &&\n          !elem.className.toLowerCase().includes(\"lazy\")\n        ) {\n          return;\n        }\n\n        for (var j = 0; j < elem.attributes.length; j++) {\n          attr = elem.attributes[j];\n          if (\n            attr.name === \"src\" ||\n            attr.name === \"srcset\" ||\n            attr.name === \"alt\"\n          ) {\n            continue;\n          }\n          var copyTo = null;\n          if (/\\.(jpg|jpeg|png|webp)\\s+\\d/.test(attr.value)) {\n            copyTo = \"srcset\";\n          } else if (/^\\s*\\S+\\.(jpg|jpeg|png|webp)\\S*\\s*$/.test(attr.value)) {\n            copyTo = \"src\";\n          }\n          if (copyTo) {\n            //if this is an img or picture, set the attribute directly\n            if (elem.tagName === \"IMG\" || elem.tagName === \"PICTURE\") {\n              elem.setAttribute(copyTo, attr.value);\n            } else if (\n              elem.tagName === \"FIGURE\" &&\n              !this._getAllNodesWithTag(elem, [\"img\", \"picture\"]).length\n            ) {\n              //if the item is a <figure> that does not contain an image or picture, create one and place it inside the figure\n              //see the nytimes-3 testcase for an example\n              var img = this._doc.createElement(\"img\");\n              img.setAttribute(copyTo, attr.value);\n              elem.appendChild(img);\n            }\n          }\n        }\n      }\n    );\n  },\n\n  _getTextDensity(e, tags) {\n    var textLength = this._getInnerText(e, true).length;\n    if (textLength === 0) {\n      return 0;\n    }\n    var childrenLength = 0;\n    var children = this._getAllNodesWithTag(e, tags);\n    this._forEachNode(\n      children,\n      child => (childrenLength += this._getInnerText(child, true).length)\n    );\n    return childrenLength / textLength;\n  },\n\n  /**\n   * Clean an element of all tags of type \"tag\" if they look fishy.\n   * \"Fishy\" is an algorithm based on content length, classnames, link density, number of images & embeds, etc.\n   *\n   * @return void\n   **/\n  _cleanConditionally(e, tag) {\n    if (!this._flagIsActive(this.FLAG_CLEAN_CONDITIONALLY)) {\n      return;\n    }\n\n    // Gather counts for other typical elements embedded within.\n    // Traverse backwards so we can remove nodes at the same time\n    // without effecting the traversal.\n    //\n    // TODO: Consider taking into account original contentScore here.\n    this._removeNodes(this._getAllNodesWithTag(e, [tag]), function (node) {\n      // First check if this node IS data table, in which case don't remove it.\n      var isDataTable = function (t) {\n        return t._readabilityDataTable;\n      };\n\n      var isList = tag === \"ul\" || tag === \"ol\";\n      if (!isList) {\n        var listLength = 0;\n        var listNodes = this._getAllNodesWithTag(node, [\"ul\", \"ol\"]);\n        this._forEachNode(\n          listNodes,\n          list => (listLength += this._getInnerText(list).length)\n        );\n        isList = listLength / this._getInnerText(node).length > 0.9;\n      }\n\n      if (tag === \"table\" && isDataTable(node)) {\n        return false;\n      }\n\n      // Next check if we're inside a data table, in which case don't remove it as well.\n      if (this._hasAncestorTag(node, \"table\", -1, isDataTable)) {\n        return false;\n      }\n\n      if (this._hasAncestorTag(node, \"code\")) {\n        return false;\n      }\n\n      // keep element if it has a data tables\n      if (\n        [...node.getElementsByTagName(\"table\")].some(\n          tbl => tbl._readabilityDataTable\n        )\n      ) {\n        return false;\n      }\n\n      var weight = this._getClassWeight(node);\n\n      this.log(\"Cleaning Conditionally\", node);\n\n      var contentScore = 0;\n\n      if (weight + contentScore < 0) {\n        return true;\n      }\n\n      if (this._getCharCount(node, \",\") < 10) {\n        // If there are not very many commas, and the number of\n        // non-paragraph elements is more than paragraphs or other\n        // ominous signs, remove the element.\n        var p = node.getElementsByTagName(\"p\").length;\n        var img = node.getElementsByTagName(\"img\").length;\n        var li = node.getElementsByTagName(\"li\").length - 100;\n        var input = node.getElementsByTagName(\"input\").length;\n        var headingDensity = this._getTextDensity(node, [\n          \"h1\",\n          \"h2\",\n          \"h3\",\n          \"h4\",\n          \"h5\",\n          \"h6\",\n        ]);\n\n        var embedCount = 0;\n        var embeds = this._getAllNodesWithTag(node, [\n          \"object\",\n          \"embed\",\n          \"iframe\",\n        ]);\n\n        for (var i = 0; i < embeds.length; i++) {\n          // If this embed has attribute that matches video regex, don't delete it.\n          for (var j = 0; j < embeds[i].attributes.length; j++) {\n            if (this._allowedVideoRegex.test(embeds[i].attributes[j].value)) {\n              return false;\n            }\n          }\n\n          // For embed with <object> tag, check inner HTML as well.\n          if (\n            embeds[i].tagName === \"object\" &&\n            this._allowedVideoRegex.test(embeds[i].innerHTML)\n          ) {\n            return false;\n          }\n\n          embedCount++;\n        }\n\n        var innerText = this._getInnerText(node);\n\n        // toss any node whose inner text contains nothing but suspicious words\n        if (\n          this.REGEXPS.adWords.test(innerText) ||\n          this.REGEXPS.loadingWords.test(innerText)\n        ) {\n          return true;\n        }\n\n        var contentLength = innerText.length;\n        var linkDensity = this._getLinkDensity(node);\n        var textishTags = [\"SPAN\", \"LI\", \"TD\"].concat(\n          Array.from(this.DIV_TO_P_ELEMS)\n        );\n        var textDensity = this._getTextDensity(node, textishTags);\n        var isFigureChild = this._hasAncestorTag(node, \"figure\");\n\n        // apply shadiness checks, then check for exceptions\n        const shouldRemoveNode = () => {\n          const errs = [];\n          if (!isFigureChild && img > 1 && p / img < 0.5) {\n            errs.push(`Bad p to img ratio (img=${img}, p=${p})`);\n          }\n          if (!isList && li > p) {\n            errs.push(`Too many li's outside of a list. (li=${li} > p=${p})`);\n          }\n          if (input > Math.floor(p / 3)) {\n            errs.push(`Too many inputs per p. (input=${input}, p=${p})`);\n          }\n          if (\n            !isList &&\n            !isFigureChild &&\n            headingDensity < 0.9 &&\n            contentLength < 25 &&\n            (img === 0 || img > 2) &&\n            linkDensity > 0\n          ) {\n            errs.push(\n              `Suspiciously short. (headingDensity=${headingDensity}, img=${img}, linkDensity=${linkDensity})`\n            );\n          }\n          if (\n            !isList &&\n            weight < 25 &&\n            linkDensity > 0.2 + this._linkDensityModifier\n          ) {\n            errs.push(\n              `Low weight and a little linky. (linkDensity=${linkDensity})`\n            );\n          }\n          if (weight >= 25 && linkDensity > 0.5 + this._linkDensityModifier) {\n            errs.push(\n              `High weight and mostly links. (linkDensity=${linkDensity})`\n            );\n          }\n          if ((embedCount === 1 && contentLength < 75) || embedCount > 1) {\n            errs.push(\n              `Suspicious embed. (embedCount=${embedCount}, contentLength=${contentLength})`\n            );\n          }\n          if (img === 0 && textDensity === 0) {\n            errs.push(\n              `No useful content. (img=${img}, textDensity=${textDensity})`\n            );\n          }\n\n          if (errs.length) {\n            this.log(\"Checks failed\", errs);\n            return true;\n          }\n\n          return false;\n        };\n\n        var haveToRemove = shouldRemoveNode();\n\n        // Allow simple lists of images to remain in pages\n        if (isList && haveToRemove) {\n          for (var x = 0; x < node.children.length; x++) {\n            let child = node.children[x];\n            // Don't filter in lists with li's that contain more than one child\n            if (child.children.length > 1) {\n              return haveToRemove;\n            }\n          }\n          let li_count = node.getElementsByTagName(\"li\").length;\n          // Only allow the list to remain if every li contains an image\n          if (img == li_count) {\n            return false;\n          }\n        }\n        return haveToRemove;\n      }\n      return false;\n    });\n  },\n\n  /**\n   * Clean out elements that match the specified conditions\n   *\n   * @param Element\n   * @param Function determines whether a node should be removed\n   * @return void\n   **/\n  _cleanMatchedNodes(e, filter) {\n    var endOfSearchMarkerNode = this._getNextNode(e, true);\n    var next = this._getNextNode(e);\n    while (next && next != endOfSearchMarkerNode) {\n      if (filter.call(this, next, next.className + \" \" + next.id)) {\n        next = this._removeAndGetNext(next);\n      } else {\n        next = this._getNextNode(next);\n      }\n    }\n  },\n\n  /**\n   * Clean out spurious headers from an Element.\n   *\n   * @param Element\n   * @return void\n   **/\n  _cleanHeaders(e) {\n    let headingNodes = this._getAllNodesWithTag(e, [\"h1\", \"h2\"]);\n    this._removeNodes(headingNodes, function (node) {\n      let shouldRemove = this._getClassWeight(node) < 0;\n      if (shouldRemove) {\n        this.log(\"Removing header with low class weight:\", node);\n      }\n      return shouldRemove;\n    });\n  },\n\n  /**\n   * Check if this node is an H1 or H2 element whose content is mostly\n   * the same as the article title.\n   *\n   * @param Element  the node to check.\n   * @return boolean indicating whether this is a title-like header.\n   */\n  _headerDuplicatesTitle(node) {\n    if (node.tagName != \"H1\" && node.tagName != \"H2\") {\n      return false;\n    }\n    var heading = this._getInnerText(node, false);\n    this.log(\"Evaluating similarity of header:\", heading, this._articleTitle);\n    return this._textSimilarity(this._articleTitle, heading) > 0.75;\n  },\n\n  _flagIsActive(flag) {\n    return (this._flags & flag) > 0;\n  },\n\n  _removeFlag(flag) {\n    this._flags = this._flags & ~flag;\n  },\n\n  _isProbablyVisible(node) {\n    // Have to null-check node.style and node.className.includes to deal with SVG and MathML nodes.\n    return (\n      (!node.style || node.style.display != \"none\") &&\n      (!node.style || node.style.visibility != \"hidden\") &&\n      !node.hasAttribute(\"hidden\") &&\n      //check for \"fallback-image\" so that wikimedia math images are displayed\n      (!node.hasAttribute(\"aria-hidden\") ||\n        node.getAttribute(\"aria-hidden\") != \"true\" ||\n        (node.className &&\n          node.className.includes &&\n          node.className.includes(\"fallback-image\")))\n    );\n  },\n\n  /**\n   * Runs readability.\n   *\n   * Workflow:\n   *  1. Prep the document by removing script tags, css, etc.\n   *  2. Build readability's DOM tree.\n   *  3. Grab the article content from the current dom tree.\n   *  4. Replace the current DOM tree with the new one.\n   *  5. Read peacefully.\n   *\n   * @return void\n   **/\n  parse() {\n    // Avoid parsing too large documents, as per configuration option\n    if (this._maxElemsToParse > 0) {\n      var numTags = this._doc.getElementsByTagName(\"*\").length;\n      if (numTags > this._maxElemsToParse) {\n        throw new Error(\n          \"Aborting parsing document; \" + numTags + \" elements found\"\n        );\n      }\n    }\n\n    // Unwrap image from noscript\n    this._unwrapNoscriptImages(this._doc);\n\n    // Extract JSON-LD metadata before removing scripts\n    var jsonLd = this._disableJSONLD ? {} : this._getJSONLD(this._doc);\n\n    // Remove script tags from the document.\n    this._removeScripts(this._doc);\n\n    this._prepDocument();\n\n    var metadata = this._getArticleMetadata(jsonLd);\n    this._metadata = metadata;\n    this._articleTitle = metadata.title;\n\n    var articleContent = this._grabArticle();\n    if (!articleContent) {\n      return null;\n    }\n\n    this.log(\"Grabbed: \" + articleContent.innerHTML);\n\n    this._postProcessContent(articleContent);\n\n    // If we haven't found an excerpt in the article's metadata, use the article's\n    // first paragraph as the excerpt. This is used for displaying a preview of\n    // the article's content.\n    if (!metadata.excerpt) {\n      var paragraphs = articleContent.getElementsByTagName(\"p\");\n      if (paragraphs.length) {\n        metadata.excerpt = paragraphs[0].textContent.trim();\n      }\n    }\n\n    var textContent = articleContent.textContent;\n    return {\n      title: this._articleTitle,\n      byline: metadata.byline || this._articleByline,\n      dir: this._articleDir,\n      lang: this._articleLang,\n      content: this._serializer(articleContent),\n      textContent,\n      length: textContent.length,\n      excerpt: metadata.excerpt,\n      siteName: metadata.siteName || this._articleSiteName,\n      publishedTime: metadata.publishedTime,\n    };\n  },\n};\n\nif (true) {\n  /* eslint-disable-next-line no-redeclare */\n  /* global module */\n  module.exports = Readability;\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@mozilla/readability/Readability.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@mozilla/readability/index.js":
/*!****************************************************!*\
  !*** ./node_modules/@mozilla/readability/index.js ***!
  \****************************************************/
/***/ ((module, __unused_webpack_exports, __webpack_require__) => {

eval("/* eslint-env node */\nvar Readability = __webpack_require__(/*! ./Readability */ \"(rsc)/./node_modules/@mozilla/readability/Readability.js\");\nvar isProbablyReaderable = __webpack_require__(/*! ./Readability-readerable */ \"(rsc)/./node_modules/@mozilla/readability/Readability-readerable.js\");\n\nmodule.exports = {\n  Readability,\n  isProbablyReaderable,\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQG1vemlsbGEvcmVhZGFiaWxpdHkvaW5kZXguanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSxrQkFBa0IsbUJBQU8sQ0FBQywrRUFBZTtBQUN6QywyQkFBMkIsbUJBQU8sQ0FBQyxxR0FBMEI7O0FBRTdEO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIvVXNlcnMvc2FudGhvc2hwYWxhbmlzYW15L3Byb2plY3RzL0FnZW50RGV2ZWxvcG1lbnQvdHdpdHRlcmJvdC90d2l0dGVyLWJvdC1kYXNoYm9hcmQvbm9kZV9tb2R1bGVzL0Btb3ppbGxhL3JlYWRhYmlsaXR5L2luZGV4LmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8qIGVzbGludC1lbnYgbm9kZSAqL1xudmFyIFJlYWRhYmlsaXR5ID0gcmVxdWlyZShcIi4vUmVhZGFiaWxpdHlcIik7XG52YXIgaXNQcm9iYWJseVJlYWRlcmFibGUgPSByZXF1aXJlKFwiLi9SZWFkYWJpbGl0eS1yZWFkZXJhYmxlXCIpO1xuXG5tb2R1bGUuZXhwb3J0cyA9IHtcbiAgUmVhZGFiaWxpdHksXG4gIGlzUHJvYmFibHlSZWFkZXJhYmxlLFxufTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@mozilla/readability/index.js\n");

/***/ })

};
;