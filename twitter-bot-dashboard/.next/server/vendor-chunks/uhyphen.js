"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/uhyphen";
exports.ids = ["vendor-chunks/uhyphen"];
exports.modules = {

/***/ "(rsc)/./node_modules/uhyphen/esm/index.js":
/*!*******************************************!*\
  !*** ./node_modules/uhyphen/esm/index.js ***!
  \*******************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (camel => camel.replace(/(([A-Z0-9])([A-Z0-9][a-z]))|(([a-z0-9]+)([A-Z]))/g, '$2$5-$3$6')\n                             .toLowerCase());\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvdWh5cGhlbi9lc20vaW5kZXguanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlO0FBQ2YsMkNBQTJDLEVBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zYW50aG9zaHBhbGFuaXNhbXkvcHJvamVjdHMvQWdlbnREZXZlbG9wbWVudC90d2l0dGVyYm90L3R3aXR0ZXItYm90LWRhc2hib2FyZC9ub2RlX21vZHVsZXMvdWh5cGhlbi9lc20vaW5kZXguanMiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgY2FtZWwgPT4gY2FtZWwucmVwbGFjZSgvKChbQS1aMC05XSkoW0EtWjAtOV1bYS16XSkpfCgoW2EtejAtOV0rKShbQS1aXSkpL2csICckMiQ1LSQzJDYnKVxuICAgICAgICAgICAgICAgICAgICAgICAgICAgICAudG9Mb3dlckNhc2UoKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/uhyphen/esm/index.js\n");

/***/ })

};
;