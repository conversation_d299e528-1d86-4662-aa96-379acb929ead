"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/@extractus";
exports.ids = ["vendor-chunks/@extractus"];
exports.modules = {

/***/ "(rsc)/./node_modules/@extractus/article-extractor/src/config.js":
/*!*****************************************************************!*\
  !*** ./node_modules/@extractus/article-extractor/src/config.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   getSanitizeHtmlOptions: () => (/* binding */ getSanitizeHtmlOptions),\n/* harmony export */   setSanitizeHtmlOptions: () => (/* binding */ setSanitizeHtmlOptions)\n/* harmony export */ });\n/* harmony import */ var _ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ndaidong/bellajs */ \"(rsc)/./node_modules/@ndaidong/bellajs/esm/mod.js\");\n// config.js\n\n\n\nconst sanitizeHtmlOptions = {\n  allowedTags: [\n    'h1', 'h2', 'h3', 'h4', 'h5', 'h6',\n    'u', 'b', 'i', 'em', 'strong', 'small', 'sup', 'sub',\n    'div', 'span', 'p', 'article', 'blockquote', 'section',\n    'details', 'summary',\n    'pre', 'code',\n    'ul', 'ol', 'li', 'dd', 'dl',\n    'table', 'th', 'tr', 'td', 'thead', 'tbody', 'tfood',\n    'fieldset', 'legend',\n    'figure', 'figcaption', 'img', 'picture',\n    'video', 'audio', 'source',\n    'iframe',\n    'progress',\n    'br', 'p', 'hr',\n    'label',\n    'abbr',\n    'a',\n    'svg',\n  ],\n  allowedAttributes: {\n    h1: ['id'],\n    h2: ['id'],\n    h3: ['id'],\n    h4: ['id'],\n    h5: ['id'],\n    h6: ['id'],\n    a: ['href', 'target', 'title'],\n    abbr: ['title'],\n    progress: ['value', 'max'],\n    img: ['src', 'srcset', 'alt', 'title'],\n    picture: ['media', 'srcset'],\n    video: ['controls', 'width', 'height', 'autoplay', 'muted', 'loop', 'src'],\n    audio: ['controls', 'width', 'height', 'autoplay', 'muted', 'loop', 'src'],\n    source: ['src', 'srcset', 'data-srcset', 'type', 'media', 'sizes'],\n    iframe: ['src', 'frameborder', 'height', 'width', 'scrolling', 'allow'],\n    svg: ['width', 'height'], // sanitize-html does not support svg fully yet\n  },\n  allowedIframeDomains: [\n    'youtube.com', 'vimeo.com', 'odysee.com',\n    'soundcloud.com', 'audius.co',\n    'github.com', 'codepen.com',\n    'twitter.com', 'facebook.com', 'instagram.com',\n  ],\n  disallowedTagsMode: 'discard',\n  allowVulnerableTags: false,\n  parseStyleAttributes: false,\n  enforceHtmlBoundary: false,\n}\n\n/**\n * @returns {SanitizeOptions}\n */\nconst getSanitizeHtmlOptions = () => {\n  return (0,_ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_0__.clone)(sanitizeHtmlOptions)\n}\n\nconst setSanitizeHtmlOptions = (opts = {}) => {\n  Object.keys(opts).forEach((key) => {\n    sanitizeHtmlOptions[key] = (0,_ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_0__.clone)(opts[key])\n  })\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@extractus/article-extractor/src/config.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@extractus/article-extractor/src/main.js":
/*!***************************************************************!*\
  !*** ./node_modules/@extractus/article-extractor/src/main.js ***!
  \***************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addTransformations: () => (/* reexport safe */ _utils_transformation_js__WEBPACK_IMPORTED_MODULE_5__.addTransformations),\n/* harmony export */   extract: () => (/* binding */ extract),\n/* harmony export */   extractFromHtml: () => (/* binding */ extractFromHtml),\n/* harmony export */   getSanitizeHtmlOptions: () => (/* reexport safe */ _config_js__WEBPACK_IMPORTED_MODULE_6__.getSanitizeHtmlOptions),\n/* harmony export */   removeTransformations: () => (/* reexport safe */ _utils_transformation_js__WEBPACK_IMPORTED_MODULE_5__.removeTransformations),\n/* harmony export */   setSanitizeHtmlOptions: () => (/* reexport safe */ _config_js__WEBPACK_IMPORTED_MODULE_6__.setSanitizeHtmlOptions)\n/* harmony export */ });\n/* harmony import */ var _ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ndaidong/bellajs */ \"(rsc)/./node_modules/@ndaidong/bellajs/esm/mod.js\");\n/* harmony import */ var _utils_retrieve_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./utils/retrieve.js */ \"(rsc)/./node_modules/@extractus/article-extractor/src/utils/retrieve.js\");\n/* harmony import */ var _utils_parseFromHtml_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./utils/parseFromHtml.js */ \"(rsc)/./node_modules/@extractus/article-extractor/src/utils/parseFromHtml.js\");\n/* harmony import */ var _utils_html_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./utils/html.js */ \"(rsc)/./node_modules/@extractus/article-extractor/src/utils/html.js\");\n/* harmony import */ var _utils_linker_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./utils/linker.js */ \"(rsc)/./node_modules/@extractus/article-extractor/src/utils/linker.js\");\n/* harmony import */ var _utils_transformation_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./utils/transformation.js */ \"(rsc)/./node_modules/@extractus/article-extractor/src/utils/transformation.js\");\n/* harmony import */ var _config_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./config.js */ \"(rsc)/./node_modules/@extractus/article-extractor/src/config.js\");\n// main.js\n\n\n\n\n\n\n\n\nconst extract = async (input, parserOptions = {}, fetchOptions = {}) => {\n  if (!(0,_ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_0__.isString)(input)) {\n    throw new Error('Input must be a string')\n  }\n\n  if (!(0,_utils_linker_js__WEBPACK_IMPORTED_MODULE_4__.isValid)(input)) {\n    return (0,_utils_parseFromHtml_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(input, null, parserOptions || {})\n  }\n  const buffer = await (0,_utils_retrieve_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(input, fetchOptions)\n  const text = buffer ? Buffer.from(buffer).toString().trim() : ''\n  if (!text) {\n    return null\n  }\n  const charset = (0,_utils_html_js__WEBPACK_IMPORTED_MODULE_3__.getCharset)(text)\n  const decoder = new TextDecoder(charset)\n  const html = decoder.decode(buffer)\n  return (0,_utils_parseFromHtml_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(html, input, parserOptions || {})\n}\n\nconst extractFromHtml = async (html, url, parserOptions = {}) => {\n  return (0,_utils_parseFromHtml_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(html, url, parserOptions)\n}\n\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@extractus/article-extractor/src/main.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@extractus/article-extractor/src/utils/extractLdSchema.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@extractus/article-extractor/src/utils/extractLdSchema.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ndaidong/bellajs */ \"(rsc)/./node_modules/@ndaidong/bellajs/esm/mod.js\");\n// utils -> extractLdSchema.js\n\n\n\nconst typeSchemas = [\n  'aboutpage',\n  'checkoutpage',\n  'collectionpage',\n  'contactpage',\n  'faqpage',\n  'itempage',\n  'medicalwebpage',\n  'profilepage',\n  'qapage',\n  'realestatelisting',\n  'searchresultspage',\n  'webpage',\n  'website',\n  'article',\n  'advertisercontentarticle',\n  'newsarticle',\n  'analysisnewsarticle',\n  'askpublicnewsarticle',\n  'backgroundnewsarticle',\n  'opinionnewsarticle',\n  'reportagenewsarticle',\n  'reviewnewsarticle',\n  'report',\n  'satiricalarticle',\n  'scholarlyarticle',\n  'medicalscholarlyarticle',\n]\n\nconst attributeLists = {\n  description: 'description',\n  image: 'image',\n  author: 'author',\n  published: 'datePublished',\n  type: '@type',\n}\n\nconst parseJson = (text) => {\n  try {\n    return JSON.parse(text)\n  } catch {\n    return {}\n  }\n}\n\nconst isAllowedLdJsonType = (ldJson) => {\n  const rootLdJsonType = ldJson['@type'] || ''\n  const arr = (0,_ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_0__.isArray)(rootLdJsonType) ? rootLdJsonType : [rootLdJsonType]\n  const ldJsonTypes = arr.filter(x => !!x)\n  return ldJsonTypes.length > 0 && ldJsonTypes.some(x => typeSchemas.includes(x.toLowerCase()))\n}\n\n/**\n * Parses JSON-LD data from a document and populates an entry object.\n * Only populates if the original entry object is empty or undefined.\n *\n * @param {Document} document - The HTML Document\n * @param {Object} entry - The entry object to merge/populate with JSON-LD.\n * @returns {Object} The entry object after being merged/populated with data.\n */\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((document, entry) => {\n  const ldSchemas = document.querySelectorAll('script[type=\"application/ld+json\"]')\n  ldSchemas.forEach(ldSchema => {\n    const ldJson = parseJson(ldSchema.textContent.replace(/[\\n\\r\\t]/g, ''))\n    if (ldJson && isAllowedLdJsonType(ldJson)) {\n      Object.entries(attributeLists).forEach(([key, attr]) => {\n        if (!entry[key] || !ldJson[attr]) {\n          return\n        }\n\n        const keyValue = ldJson[attr]\n        const val = (0,_ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_0__.isArray)(keyValue) ? keyValue[0] : (0,_ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_0__.isObject)(keyValue) ? keyValue?.name || '' : keyValue\n        if ((0,_ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_0__.isString)(val) && val !== '') {\n          entry[key] = val.trim()\n        }\n      })\n    }\n  })\n\n  return entry\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGV4dHJhY3R1cy9hcnRpY2xlLWV4dHJhY3Rvci9zcmMvdXRpbHMvZXh0cmFjdExkU2NoZW1hLmpzIiwibWFwcGluZ3MiOiI7Ozs7O0FBQUE7O0FBRStEOztBQUUvRDtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQSxJQUFJO0FBQ0o7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQSxjQUFjLDBEQUFPO0FBQ3JCO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBLFdBQVcsVUFBVTtBQUNyQixXQUFXLFFBQVE7QUFDbkIsYUFBYSxRQUFRO0FBQ3JCO0FBQ0EsaUVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0Esb0JBQW9CLDBEQUFPLDJCQUEyQiwyREFBUTtBQUM5RCxZQUFZLDJEQUFRO0FBQ3BCO0FBQ0E7QUFDQSxPQUFPO0FBQ1A7QUFDQSxHQUFHOztBQUVIO0FBQ0EsQ0FBQyIsInNvdXJjZXMiOlsiL1VzZXJzL3NhbnRob3NocGFsYW5pc2FteS9wcm9qZWN0cy9BZ2VudERldmVsb3BtZW50L3R3aXR0ZXJib3QvdHdpdHRlci1ib3QtZGFzaGJvYXJkL25vZGVfbW9kdWxlcy9AZXh0cmFjdHVzL2FydGljbGUtZXh0cmFjdG9yL3NyYy91dGlscy9leHRyYWN0TGRTY2hlbWEuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gdXRpbHMgLT4gZXh0cmFjdExkU2NoZW1hLmpzXG5cbmltcG9ydCB7IGlzQXJyYXksIGlzT2JqZWN0LCBpc1N0cmluZyB9IGZyb20gJ0BuZGFpZG9uZy9iZWxsYWpzJ1xuXG5jb25zdCB0eXBlU2NoZW1hcyA9IFtcbiAgJ2Fib3V0cGFnZScsXG4gICdjaGVja291dHBhZ2UnLFxuICAnY29sbGVjdGlvbnBhZ2UnLFxuICAnY29udGFjdHBhZ2UnLFxuICAnZmFxcGFnZScsXG4gICdpdGVtcGFnZScsXG4gICdtZWRpY2Fsd2VicGFnZScsXG4gICdwcm9maWxlcGFnZScsXG4gICdxYXBhZ2UnLFxuICAncmVhbGVzdGF0ZWxpc3RpbmcnLFxuICAnc2VhcmNocmVzdWx0c3BhZ2UnLFxuICAnd2VicGFnZScsXG4gICd3ZWJzaXRlJyxcbiAgJ2FydGljbGUnLFxuICAnYWR2ZXJ0aXNlcmNvbnRlbnRhcnRpY2xlJyxcbiAgJ25ld3NhcnRpY2xlJyxcbiAgJ2FuYWx5c2lzbmV3c2FydGljbGUnLFxuICAnYXNrcHVibGljbmV3c2FydGljbGUnLFxuICAnYmFja2dyb3VuZG5ld3NhcnRpY2xlJyxcbiAgJ29waW5pb25uZXdzYXJ0aWNsZScsXG4gICdyZXBvcnRhZ2VuZXdzYXJ0aWNsZScsXG4gICdyZXZpZXduZXdzYXJ0aWNsZScsXG4gICdyZXBvcnQnLFxuICAnc2F0aXJpY2FsYXJ0aWNsZScsXG4gICdzY2hvbGFybHlhcnRpY2xlJyxcbiAgJ21lZGljYWxzY2hvbGFybHlhcnRpY2xlJyxcbl1cblxuY29uc3QgYXR0cmlidXRlTGlzdHMgPSB7XG4gIGRlc2NyaXB0aW9uOiAnZGVzY3JpcHRpb24nLFxuICBpbWFnZTogJ2ltYWdlJyxcbiAgYXV0aG9yOiAnYXV0aG9yJyxcbiAgcHVibGlzaGVkOiAnZGF0ZVB1Ymxpc2hlZCcsXG4gIHR5cGU6ICdAdHlwZScsXG59XG5cbmNvbnN0IHBhcnNlSnNvbiA9ICh0ZXh0KSA9PiB7XG4gIHRyeSB7XG4gICAgcmV0dXJuIEpTT04ucGFyc2UodGV4dClcbiAgfSBjYXRjaCB7XG4gICAgcmV0dXJuIHt9XG4gIH1cbn1cblxuY29uc3QgaXNBbGxvd2VkTGRKc29uVHlwZSA9IChsZEpzb24pID0+IHtcbiAgY29uc3Qgcm9vdExkSnNvblR5cGUgPSBsZEpzb25bJ0B0eXBlJ10gfHwgJydcbiAgY29uc3QgYXJyID0gaXNBcnJheShyb290TGRKc29uVHlwZSkgPyByb290TGRKc29uVHlwZSA6IFtyb290TGRKc29uVHlwZV1cbiAgY29uc3QgbGRKc29uVHlwZXMgPSBhcnIuZmlsdGVyKHggPT4gISF4KVxuICByZXR1cm4gbGRKc29uVHlwZXMubGVuZ3RoID4gMCAmJiBsZEpzb25UeXBlcy5zb21lKHggPT4gdHlwZVNjaGVtYXMuaW5jbHVkZXMoeC50b0xvd2VyQ2FzZSgpKSlcbn1cblxuLyoqXG4gKiBQYXJzZXMgSlNPTi1MRCBkYXRhIGZyb20gYSBkb2N1bWVudCBhbmQgcG9wdWxhdGVzIGFuIGVudHJ5IG9iamVjdC5cbiAqIE9ubHkgcG9wdWxhdGVzIGlmIHRoZSBvcmlnaW5hbCBlbnRyeSBvYmplY3QgaXMgZW1wdHkgb3IgdW5kZWZpbmVkLlxuICpcbiAqIEBwYXJhbSB7RG9jdW1lbnR9IGRvY3VtZW50IC0gVGhlIEhUTUwgRG9jdW1lbnRcbiAqIEBwYXJhbSB7T2JqZWN0fSBlbnRyeSAtIFRoZSBlbnRyeSBvYmplY3QgdG8gbWVyZ2UvcG9wdWxhdGUgd2l0aCBKU09OLUxELlxuICogQHJldHVybnMge09iamVjdH0gVGhlIGVudHJ5IG9iamVjdCBhZnRlciBiZWluZyBtZXJnZWQvcG9wdWxhdGVkIHdpdGggZGF0YS5cbiAqL1xuZXhwb3J0IGRlZmF1bHQgKGRvY3VtZW50LCBlbnRyeSkgPT4ge1xuICBjb25zdCBsZFNjaGVtYXMgPSBkb2N1bWVudC5xdWVyeVNlbGVjdG9yQWxsKCdzY3JpcHRbdHlwZT1cImFwcGxpY2F0aW9uL2xkK2pzb25cIl0nKVxuICBsZFNjaGVtYXMuZm9yRWFjaChsZFNjaGVtYSA9PiB7XG4gICAgY29uc3QgbGRKc29uID0gcGFyc2VKc29uKGxkU2NoZW1hLnRleHRDb250ZW50LnJlcGxhY2UoL1tcXG5cXHJcXHRdL2csICcnKSlcbiAgICBpZiAobGRKc29uICYmIGlzQWxsb3dlZExkSnNvblR5cGUobGRKc29uKSkge1xuICAgICAgT2JqZWN0LmVudHJpZXMoYXR0cmlidXRlTGlzdHMpLmZvckVhY2goKFtrZXksIGF0dHJdKSA9PiB7XG4gICAgICAgIGlmICghZW50cnlba2V5XSB8fCAhbGRKc29uW2F0dHJdKSB7XG4gICAgICAgICAgcmV0dXJuXG4gICAgICAgIH1cblxuICAgICAgICBjb25zdCBrZXlWYWx1ZSA9IGxkSnNvblthdHRyXVxuICAgICAgICBjb25zdCB2YWwgPSBpc0FycmF5KGtleVZhbHVlKSA/IGtleVZhbHVlWzBdIDogaXNPYmplY3Qoa2V5VmFsdWUpID8ga2V5VmFsdWU/Lm5hbWUgfHwgJycgOiBrZXlWYWx1ZVxuICAgICAgICBpZiAoaXNTdHJpbmcodmFsKSAmJiB2YWwgIT09ICcnKSB7XG4gICAgICAgICAgZW50cnlba2V5XSA9IHZhbC50cmltKClcbiAgICAgICAgfVxuICAgICAgfSlcbiAgICB9XG4gIH0pXG5cbiAgcmV0dXJuIGVudHJ5XG59XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@extractus/article-extractor/src/utils/extractLdSchema.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@extractus/article-extractor/src/utils/extractMetaData.js":
/*!********************************************************************************!*\
  !*** ./node_modules/@extractus/article-extractor/src/utils/extractMetaData.js ***!
  \********************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var linkedom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! linkedom */ \"(rsc)/./node_modules/linkedom/esm/index.js\");\n/* harmony import */ var _extractLdSchema_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./extractLdSchema.js */ \"(rsc)/./node_modules/@extractus/article-extractor/src/utils/extractLdSchema.js\");\n/* harmony import */ var _findDate_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./findDate.js */ \"(rsc)/./node_modules/@extractus/article-extractor/src/utils/findDate.js\");\n// utils -> extractMetaData\n\n\n\n\n\n/**\n * @param {Element} node\n * @param {Object} attributeLists\n * @returns {?{key: string, content: string}}\n */\nfunction getMetaContentByNameOrProperty (node, attributeLists) {\n  const content = node.getAttribute('content')\n  if (!content) return null\n\n  const property = node\n    .getAttribute('property')?.toLowerCase() ??\n    node.getAttribute('itemprop')?.toLowerCase()\n\n  const name = node.getAttribute('name')?.toLowerCase()\n\n  for (const [key, attrs] of Object.entries(attributeLists)) {\n    if (attrs.includes(property) || attrs.includes(name)) {\n      return { key, content }\n    }\n  }\n\n  return null\n}\n\n/**\n * @param html {string}\n * @returns {{image: string, author: string, amphtml: string, description: string, canonical: string, source: string, published: string, title: string, url: string, shortlink: string, favicon: string, type: string}}\n */\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((html) => {\n  const entry = {\n    url: '',\n    shortlink: '',\n    amphtml: '',\n    canonical: '',\n    title: '',\n    description: '',\n    image: '',\n    author: '',\n    source: '',\n    published: '',\n    favicon: '',\n    type: '',\n  }\n\n  const sourceAttrs = [\n    'application-name',\n    'og:site_name',\n    'twitter:site',\n    'dc.title',\n  ]\n  const urlAttrs = [\n    'og:url',\n    'twitter:url',\n    'parsely-link',\n  ]\n  const titleAttrs = [\n    'title',\n    'og:title',\n    'twitter:title',\n    'parsely-title',\n  ]\n  const descriptionAttrs = [\n    'description',\n    'og:description',\n    'twitter:description',\n    'parsely-description',\n  ]\n  const imageAttrs = [\n    'image',\n    'og:image',\n    'og:image:url',\n    'og:image:secure_url',\n    'twitter:image',\n    'twitter:image:src',\n    'parsely-image-url',\n  ]\n  const authorAttrs = [\n    'author',\n    'creator',\n    'og:creator',\n    'article:author',\n    'twitter:creator',\n    'dc.creator',\n    'parsely-author',\n  ]\n  const publishedTimeAttrs = [\n    'article:published_time',\n    'article:modified_time',\n    'og:updated_time',\n    'dc.date',\n    'dc.date.issued',\n    'dc.date.created',\n    'dc:created',\n    'dcterms.date',\n    'datepublished',\n    'datemodified',\n    'updated_time',\n    'modified_time',\n    'published_time',\n    'release_date',\n    'date',\n    'parsely-pub-date',\n  ]\n  const typeAttrs = [\n    'og:type',\n  ]\n\n  const attributeLists = {\n    source: sourceAttrs,\n    url: urlAttrs,\n    title: titleAttrs,\n    description: descriptionAttrs,\n    image: imageAttrs,\n    author: authorAttrs,\n    published: publishedTimeAttrs,\n    type: typeAttrs,\n  }\n\n  const doc = new linkedom__WEBPACK_IMPORTED_MODULE_0__.DOMParser().parseFromString(html, 'text/html')\n  entry.title = doc.querySelector('head > title')?.innerText\n\n  Array.from(doc.getElementsByTagName('link')).forEach(node => {\n    const rel = node.getAttribute('rel')\n    const href = node.getAttribute('href')\n    if (rel && href) {\n      entry[rel] = href\n      if (rel === 'icon' || rel === 'shortcut icon') {\n        entry.favicon = href\n      }\n    }\n  })\n\n  Array.from(doc.getElementsByTagName('meta')).forEach(node => {\n    const result = getMetaContentByNameOrProperty(node, attributeLists)\n    const val = result?.content || ''\n    if (val !== '') {\n      entry[result.key] = val\n    }\n  })\n\n  const metadata = (0,_extractLdSchema_js__WEBPACK_IMPORTED_MODULE_1__[\"default\"])(doc, entry)\n\n  if (!metadata.published) {\n    metadata.published = (0,_findDate_js__WEBPACK_IMPORTED_MODULE_2__[\"default\"])(doc) || ''\n  }\n\n  return metadata\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@extractus/article-extractor/src/utils/extractMetaData.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@extractus/article-extractor/src/utils/extractWithReadability.js":
/*!***************************************************************************************!*\
  !*** ./node_modules/@extractus/article-extractor/src/utils/extractWithReadability.js ***!
  \***************************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__),\n/* harmony export */   extractTitleWithReadability: () => (/* binding */ extractTitleWithReadability)\n/* harmony export */ });\n/* harmony import */ var _mozilla_readability__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @mozilla/readability */ \"(rsc)/./node_modules/@mozilla/readability/index.js\");\n/* harmony import */ var linkedom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! linkedom */ \"(rsc)/./node_modules/linkedom/esm/index.js\");\n/* harmony import */ var _ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @ndaidong/bellajs */ \"(rsc)/./node_modules/@ndaidong/bellajs/esm/mod.js\");\n// utils/extractWithReadability\n\n\n\n\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((html, url = '') => {\n  if (!(0,_ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_2__.isString)(html)) {\n    return null\n  }\n  const doc = new linkedom__WEBPACK_IMPORTED_MODULE_1__.DOMParser().parseFromString(html, 'text/html')\n  const base = doc.createElement('base')\n  base.setAttribute('href', url)\n  doc.head.appendChild(base)\n  const reader = new _mozilla_readability__WEBPACK_IMPORTED_MODULE_0__.Readability(doc, {\n    keepClasses: true,\n  })\n  const result = reader.parse() ?? {}\n  return result.textContent ? result.content : null\n});\n\nfunction extractTitleWithReadability (html) {\n  if (!(0,_ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_2__.isString)(html)) {\n    return null\n  }\n  const doc = new linkedom__WEBPACK_IMPORTED_MODULE_1__.DOMParser().parseFromString(html, 'text/html')\n  const reader = new _mozilla_readability__WEBPACK_IMPORTED_MODULE_0__.Readability(doc)\n  return reader._getArticleTitle() || null\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGV4dHJhY3R1cy9hcnRpY2xlLWV4dHJhY3Rvci9zcmMvdXRpbHMvZXh0cmFjdFdpdGhSZWFkYWJpbGl0eS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7OztBQUFBOztBQUVrRDtBQUNkO0FBQ1E7O0FBRTVDLGlFQUFlO0FBQ2YsT0FBTywyREFBUTtBQUNmO0FBQ0E7QUFDQSxrQkFBa0IsK0NBQVM7QUFDM0I7QUFDQTtBQUNBO0FBQ0EscUJBQXFCLDZEQUFXO0FBQ2hDO0FBQ0EsR0FBRztBQUNIO0FBQ0E7QUFDQSxDQUFDOztBQUVNO0FBQ1AsT0FBTywyREFBUTtBQUNmO0FBQ0E7QUFDQSxrQkFBa0IsK0NBQVM7QUFDM0IscUJBQXFCLDZEQUFXO0FBQ2hDO0FBQ0EiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zYW50aG9zaHBhbGFuaXNhbXkvcHJvamVjdHMvQWdlbnREZXZlbG9wbWVudC90d2l0dGVyYm90L3R3aXR0ZXItYm90LWRhc2hib2FyZC9ub2RlX21vZHVsZXMvQGV4dHJhY3R1cy9hcnRpY2xlLWV4dHJhY3Rvci9zcmMvdXRpbHMvZXh0cmFjdFdpdGhSZWFkYWJpbGl0eS5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyB1dGlscy9leHRyYWN0V2l0aFJlYWRhYmlsaXR5XG5cbmltcG9ydCB7IFJlYWRhYmlsaXR5IH0gZnJvbSAnQG1vemlsbGEvcmVhZGFiaWxpdHknXG5pbXBvcnQgeyBET01QYXJzZXIgfSBmcm9tICdsaW5rZWRvbSdcbmltcG9ydCB7IGlzU3RyaW5nIH0gZnJvbSAnQG5kYWlkb25nL2JlbGxhanMnXG5cbmV4cG9ydCBkZWZhdWx0IChodG1sLCB1cmwgPSAnJykgPT4ge1xuICBpZiAoIWlzU3RyaW5nKGh0bWwpKSB7XG4gICAgcmV0dXJuIG51bGxcbiAgfVxuICBjb25zdCBkb2MgPSBuZXcgRE9NUGFyc2VyKCkucGFyc2VGcm9tU3RyaW5nKGh0bWwsICd0ZXh0L2h0bWwnKVxuICBjb25zdCBiYXNlID0gZG9jLmNyZWF0ZUVsZW1lbnQoJ2Jhc2UnKVxuICBiYXNlLnNldEF0dHJpYnV0ZSgnaHJlZicsIHVybClcbiAgZG9jLmhlYWQuYXBwZW5kQ2hpbGQoYmFzZSlcbiAgY29uc3QgcmVhZGVyID0gbmV3IFJlYWRhYmlsaXR5KGRvYywge1xuICAgIGtlZXBDbGFzc2VzOiB0cnVlLFxuICB9KVxuICBjb25zdCByZXN1bHQgPSByZWFkZXIucGFyc2UoKSA/PyB7fVxuICByZXR1cm4gcmVzdWx0LnRleHRDb250ZW50ID8gcmVzdWx0LmNvbnRlbnQgOiBudWxsXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBleHRyYWN0VGl0bGVXaXRoUmVhZGFiaWxpdHkgKGh0bWwpIHtcbiAgaWYgKCFpc1N0cmluZyhodG1sKSkge1xuICAgIHJldHVybiBudWxsXG4gIH1cbiAgY29uc3QgZG9jID0gbmV3IERPTVBhcnNlcigpLnBhcnNlRnJvbVN0cmluZyhodG1sLCAndGV4dC9odG1sJylcbiAgY29uc3QgcmVhZGVyID0gbmV3IFJlYWRhYmlsaXR5KGRvYylcbiAgcmV0dXJuIHJlYWRlci5fZ2V0QXJ0aWNsZVRpdGxlKCkgfHwgbnVsbFxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@extractus/article-extractor/src/utils/extractWithReadability.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@extractus/article-extractor/src/utils/findDate.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@extractus/article-extractor/src/utils/findDate.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* export default binding */ __WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n\n/**\n * Convert date format to YYYY-MM-DD\n *\n * @param {string} dateString\n * @returns {string} YYYY-MM-DD\n */\nfunction convertDateFormat (dateString) {\n  const parts = dateString.split('/')\n  if (parts.length !== 3) return dateString\n\n  let year, month, day\n\n  if (parseInt(parts[0]) > 12) {\n    [day, month, year] = parts\n  } else {\n    [month, day, year] = parts\n  }\n\n  year = year.length === 2 ? '20' + year : year\n  return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}T00:00:00`\n}\n\n/**\n * Look for the publication date in the body of the content.\n *\n * @param {Document} document - The HTML Document\n * @returns {string} The date string\n */\n/* harmony default export */ function __WEBPACK_DEFAULT_EXPORT__(doc) {\n  const datePatterns = [\n    /\\d{4}-\\d{2}-\\d{2}/,\n    /\\d{1,2}\\/\\d{1,2}\\/\\d{2,4}/,\n  ]\n\n  const findDate = (element) => {\n    for (const pattern of datePatterns) {\n      const match = element.textContent.match(pattern)\n      if (match) return convertDateFormat(match[0])\n    }\n    return ''\n  }\n\n  const priorityElements = doc.querySelectorAll('time, [datetime], [itemprop~=datePublished], [itemprop~=dateCreated]')\n  for (const el of priorityElements) {\n    const date = el.getAttribute('datetime') || el.getAttribute('content') || findDate(el)\n    if (date) return date\n  }\n\n  const secondaryElements = doc.querySelectorAll('p, span, div')\n  for (const el of secondaryElements) {\n    const date = findDate(el)\n    if (date) return date\n  }\n\n  return ''\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@extractus/article-extractor/src/utils/findDate.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@extractus/article-extractor/src/utils/getTimeToRead.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@extractus/article-extractor/src/utils/getTimeToRead.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n// utils -> getTimeToRead\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = ((text, wordsPerMinute) => {\n  const words = text.trim().split(/\\s+/g).length\n  const minToRead = words / wordsPerMinute\n  const secToRead = Math.ceil(minToRead * 60)\n  return secToRead\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMvQGV4dHJhY3R1cy9hcnRpY2xlLWV4dHJhY3Rvci9zcmMvdXRpbHMvZ2V0VGltZVRvUmVhZC5qcyIsIm1hcHBpbmdzIjoiOzs7O0FBQUE7O0FBRUEsaUVBQWU7QUFDZjtBQUNBO0FBQ0E7QUFDQTtBQUNBLENBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zYW50aG9zaHBhbGFuaXNhbXkvcHJvamVjdHMvQWdlbnREZXZlbG9wbWVudC90d2l0dGVyYm90L3R3aXR0ZXItYm90LWRhc2hib2FyZC9ub2RlX21vZHVsZXMvQGV4dHJhY3R1cy9hcnRpY2xlLWV4dHJhY3Rvci9zcmMvdXRpbHMvZ2V0VGltZVRvUmVhZC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyB1dGlscyAtPiBnZXRUaW1lVG9SZWFkXG5cbmV4cG9ydCBkZWZhdWx0ICh0ZXh0LCB3b3Jkc1Blck1pbnV0ZSkgPT4ge1xuICBjb25zdCB3b3JkcyA9IHRleHQudHJpbSgpLnNwbGl0KC9cXHMrL2cpLmxlbmd0aFxuICBjb25zdCBtaW5Ub1JlYWQgPSB3b3JkcyAvIHdvcmRzUGVyTWludXRlXG4gIGNvbnN0IHNlY1RvUmVhZCA9IE1hdGguY2VpbChtaW5Ub1JlYWQgKiA2MClcbiAgcmV0dXJuIHNlY1RvUmVhZFxufVxuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@extractus/article-extractor/src/utils/getTimeToRead.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@extractus/article-extractor/src/utils/html.js":
/*!*********************************************************************!*\
  !*** ./node_modules/@extractus/article-extractor/src/utils/html.js ***!
  \*********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cleanify: () => (/* binding */ cleanify),\n/* harmony export */   getCharset: () => (/* binding */ getCharset),\n/* harmony export */   purify: () => (/* binding */ purify)\n/* harmony export */ });\n/* harmony import */ var linkedom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! linkedom */ \"(rsc)/./node_modules/linkedom/esm/index.js\");\n/* harmony import */ var sanitize_html__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! sanitize-html */ \"(rsc)/./node_modules/sanitize-html/index.js\");\n/* harmony import */ var _ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @ndaidong/bellajs */ \"(rsc)/./node_modules/@ndaidong/bellajs/esm/mod.js\");\n/* harmony import */ var _config_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../config.js */ \"(rsc)/./node_modules/@extractus/article-extractor/src/config.js\");\n// utils -> html\n\n\n\n\n\n\n\nconst purify = (html) => {\n  return sanitize_html__WEBPACK_IMPORTED_MODULE_3__(html, {\n    allowedTags: false,\n    allowedAttributes: false,\n    allowVulnerableTags: true,\n  })\n}\n\nconst WS_REGEXP = /^[\\s\\f\\n\\r\\t\\u1680\\u180e\\u2000\\u2001\\u2002\\u2003\\u2004\\u2005\\u2006\\u2007\\u2008\\u2009\\u200a\\u2028\\u2029\\u202f\\u205f\\u3000\\ufeff\\x09\\x0a\\x0b\\x0c\\x0d\\x20\\xa0]+$/ // eslint-disable-line\n\nconst stripMultiLinebreaks = (str) => {\n  return str.replace(/(\\r\\n|\\n|\\u2424){2,}/g, '\\n').split('\\n').map((line) => {\n    return WS_REGEXP.test(line) ? line.trim() : line\n  }).filter((line) => {\n    return line.length > 0\n  }).join('\\n')\n}\n\nconst stripMultispaces = (str) => {\n  return str.replace(WS_REGEXP, ' ').trim()\n}\n\nconst getCharset = (html) => {\n  const doc = new linkedom__WEBPACK_IMPORTED_MODULE_0__.DOMParser().parseFromString(html, 'text/html')\n  const m = doc.querySelector('meta[charset]') || null\n  let charset = m ? m.getAttribute('charset') : ''\n  if (!charset) {\n    const h = doc.querySelector('meta[http-equiv=\"content-type\"]') || null\n    charset = h ? h.getAttribute('content')?.split(';')[1]?.replace('charset=', '')?.trim() : ''\n  }\n  return charset?.toLowerCase() || 'utf8'\n}\n\nconst cleanify = (inputHtml) => {\n  const doc = new linkedom__WEBPACK_IMPORTED_MODULE_0__.DOMParser().parseFromString(inputHtml, 'text/html')\n  const html = doc.documentElement.innerHTML\n  return (0,_ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_1__.pipe)(\n    input => sanitize_html__WEBPACK_IMPORTED_MODULE_3__(input, (0,_config_js__WEBPACK_IMPORTED_MODULE_2__.getSanitizeHtmlOptions)()),\n    input => stripMultiLinebreaks(input),\n    input => stripMultispaces(input)\n  )(html)\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@extractus/article-extractor/src/utils/html.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@extractus/article-extractor/src/utils/linker.js":
/*!***********************************************************************!*\
  !*** ./node_modules/@extractus/article-extractor/src/utils/linker.js ***!
  \***********************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   absolutify: () => (/* binding */ absolutify),\n/* harmony export */   chooseBestUrl: () => (/* binding */ chooseBestUrl),\n/* harmony export */   getDomain: () => (/* binding */ getDomain),\n/* harmony export */   isValid: () => (/* binding */ isValid),\n/* harmony export */   normalize: () => (/* binding */ normalize),\n/* harmony export */   purify: () => (/* binding */ purify)\n/* harmony export */ });\n/* harmony import */ var linkedom__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! linkedom */ \"(rsc)/./node_modules/linkedom/esm/index.js\");\n/* harmony import */ var _similarity_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./similarity.js */ \"(rsc)/./node_modules/@extractus/article-extractor/src/utils/similarity.js\");\n// utils -> linker\n\n\n\n\n\nconst isValid = (url = '') => {\n  try {\n    const ourl = new URL(url)\n    return ourl !== null && ourl.protocol.startsWith('http')\n  } catch {\n    return false\n  }\n}\n\nconst chooseBestUrl = (candidates = [], title = '') => {\n  const ranking = (0,_similarity_js__WEBPACK_IMPORTED_MODULE_1__.findBestMatch)(title, candidates)\n  return ranking.bestMatch.target\n}\n\nconst absolutify = (fullUrl = '', relativeUrl = '') => {\n  try {\n    const result = new URL(relativeUrl, fullUrl)\n    return result.toString()\n  } catch {\n    return ''\n  }\n}\n\nconst blacklistKeys = [\n  'CNDID',\n  '__twitter_impression',\n  '_hsenc',\n  '_openstat',\n  'action_object_map',\n  'action_ref_map',\n  'action_type_map',\n  'amp',\n  'fb_action_ids',\n  'fb_action_types',\n  'fb_ref',\n  'fb_source',\n  'fbclid',\n  'ga_campaign',\n  'ga_content',\n  'ga_medium',\n  'ga_place',\n  'ga_source',\n  'ga_term',\n  'gs_l',\n  'hmb_campaign',\n  'hmb_medium',\n  'hmb_source',\n  'mbid',\n  'mc_cid',\n  'mc_eid',\n  'mkt_tok',\n  'referrer',\n  'spJobID',\n  'spMailingID',\n  'spReportId',\n  'spUserID',\n  'utm_brand',\n  'utm_campaign',\n  'utm_cid',\n  'utm_content',\n  'utm_int',\n  'utm_mailing',\n  'utm_medium',\n  'utm_name',\n  'utm_place',\n  'utm_pubreferrer',\n  'utm_reader',\n  'utm_social',\n  'utm_source',\n  'utm_swu',\n  'utm_term',\n  'utm_userid',\n  'utm_viz_id',\n  'wt_mc_o',\n  'yclid',\n  'WT.mc_id',\n  'WT.mc_ev',\n  'WT.srch',\n  'pk_source',\n  'pk_medium',\n  'pk_campaign',\n]\n\nconst purify = (url) => {\n  try {\n    const pureUrl = new URL(url)\n\n    blacklistKeys.forEach((key) => {\n      pureUrl.searchParams.delete(key)\n    })\n\n    return pureUrl.toString().replace(pureUrl.hash, '')\n  } catch {\n    return null\n  }\n}\n\n/**\n * @param inputHtml {string}\n * @param url {string}\n * @returns article {string}\n */\nconst normalize = (html, url) => {\n  const doc = new linkedom__WEBPACK_IMPORTED_MODULE_0__.DOMParser().parseFromString(html, 'text/html')\n\n  Array.from(doc.getElementsByTagName('a')).forEach((element) => {\n    const href = element.getAttribute('href')\n    if (href) {\n      element.setAttribute('href', absolutify(url, href))\n      element.setAttribute('target', '_blank')\n    }\n  })\n\n  Array.from(doc.getElementsByTagName('img')).forEach((element) => {\n    const src = element.getAttribute('data-src') ?? element.getAttribute('src')\n    if (src) {\n      element.setAttribute('src', absolutify(url, src))\n    }\n  })\n\n  return Array.from(doc.childNodes).map(element => element.outerHTML).join('')\n}\n\nconst getDomain = (url) => {\n  const host = (new URL(url)).host\n  return host.replace('www.', '')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@extractus/article-extractor/src/utils/linker.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@extractus/article-extractor/src/utils/parseFromHtml.js":
/*!******************************************************************************!*\
  !*** ./node_modules/@extractus/article-extractor/src/utils/parseFromHtml.js ***!
  \******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var _ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ndaidong/bellajs */ \"(rsc)/./node_modules/@ndaidong/bellajs/esm/mod.js\");\n/* harmony import */ var _html_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./html.js */ \"(rsc)/./node_modules/@extractus/article-extractor/src/utils/html.js\");\n/* harmony import */ var _linker_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./linker.js */ \"(rsc)/./node_modules/@extractus/article-extractor/src/utils/linker.js\");\n/* harmony import */ var _extractMetaData_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ./extractMetaData.js */ \"(rsc)/./node_modules/@extractus/article-extractor/src/utils/extractMetaData.js\");\n/* harmony import */ var _extractWithReadability_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./extractWithReadability.js */ \"(rsc)/./node_modules/@extractus/article-extractor/src/utils/extractWithReadability.js\");\n/* harmony import */ var _transformation_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./transformation.js */ \"(rsc)/./node_modules/@extractus/article-extractor/src/utils/transformation.js\");\n/* harmony import */ var _getTimeToRead_js__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! ./getTimeToRead.js */ \"(rsc)/./node_modules/@extractus/article-extractor/src/utils/getTimeToRead.js\");\n// utils -> parseFromHtml\n\n\n\n\n\n\n\n\n\n\n\n\n\n\n\nconst summarize = (desc, txt, threshold, maxlen) => { // eslint-disable-line\n  return desc.length > threshold\n    ? desc\n    : (0,_ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_0__.truncate)(txt, maxlen).replace(/\\n/g, ' ')\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (inputHtml, inputUrl = '', parserOptions = {}) => {\n  const pureHtml = (0,_html_js__WEBPACK_IMPORTED_MODULE_1__.purify)(inputHtml)\n  const meta = (0,_extractMetaData_js__WEBPACK_IMPORTED_MODULE_3__[\"default\"])(pureHtml)\n\n  let title = meta.title\n\n  const {\n    url,\n    shortlink,\n    amphtml,\n    canonical,\n    description: metaDesc,\n    image: metaImg,\n    author,\n    published,\n    favicon: metaFav,\n    type,\n  } = meta\n\n  const {\n    wordsPerMinute = 300,\n    descriptionTruncateLen = 210,\n    descriptionLengthThreshold = 180,\n    contentLengthThreshold = 200,\n  } = parserOptions\n\n  // gather title\n  if (!title) {\n    title = (0,_extractWithReadability_js__WEBPACK_IMPORTED_MODULE_4__.extractTitleWithReadability)(pureHtml, inputUrl)\n  }\n  if (!title) {\n    return null\n  }\n\n  // gather urls to choose the best url later\n  const links = (0,_ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_0__.unique)(\n    [url, shortlink, amphtml, canonical, inputUrl]\n      .filter(_linker_js__WEBPACK_IMPORTED_MODULE_2__.isValid)\n      .map(_linker_js__WEBPACK_IMPORTED_MODULE_2__.purify)\n  )\n\n  if (!links.length) {\n    return null\n  }\n\n  // choose the best url, which one looks like title the most\n  const bestUrl = (0,_linker_js__WEBPACK_IMPORTED_MODULE_2__.chooseBestUrl)(links, title)\n\n  const fns = (0,_ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_0__.pipe)(\n    (input) => {\n      return (0,_linker_js__WEBPACK_IMPORTED_MODULE_2__.normalize)(input, bestUrl)\n    },\n    (input) => {\n      return (0,_transformation_js__WEBPACK_IMPORTED_MODULE_5__.execPreParser)(input, links)\n    },\n    (input) => {\n      return (0,_extractWithReadability_js__WEBPACK_IMPORTED_MODULE_4__[\"default\"])(input, bestUrl)\n    },\n    (input) => {\n      return input ? (0,_transformation_js__WEBPACK_IMPORTED_MODULE_5__.execPostParser)(input, links) : null\n    },\n    (input) => {\n      return input ? (0,_html_js__WEBPACK_IMPORTED_MODULE_1__.cleanify)(input) : null\n    }\n  )\n\n  const content = fns(inputHtml)\n\n  if (!content) {\n    return null\n  }\n\n  const textContent = (0,_ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_0__.stripTags)(content)\n  if (textContent.length < contentLengthThreshold) {\n    return null\n  }\n\n  const description = summarize(\n    metaDesc,\n    textContent,\n    descriptionLengthThreshold,\n    descriptionTruncateLen\n  )\n\n  const image = metaImg ? (0,_linker_js__WEBPACK_IMPORTED_MODULE_2__.absolutify)(bestUrl, metaImg) : ''\n  const favicon = metaFav ? (0,_linker_js__WEBPACK_IMPORTED_MODULE_2__.absolutify)(bestUrl, metaFav) : ''\n\n  return {\n    url: bestUrl,\n    title,\n    description,\n    links,\n    image,\n    content,\n    author,\n    favicon,\n    source: (0,_linker_js__WEBPACK_IMPORTED_MODULE_2__.getDomain)(bestUrl),\n    published,\n    ttr: (0,_getTimeToRead_js__WEBPACK_IMPORTED_MODULE_6__[\"default\"])(textContent, wordsPerMinute),\n    type,\n  }\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@extractus/article-extractor/src/utils/parseFromHtml.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@extractus/article-extractor/src/utils/retrieve.js":
/*!*************************************************************************!*\
  !*** ./node_modules/@extractus/article-extractor/src/utils/retrieve.js ***!
  \*************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony import */ var cross_fetch__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! cross-fetch */ \"(rsc)/./node_modules/cross-fetch/dist/node-ponyfill.js\");\n// utils -> retrieve\n\n\n\nconst profetch = async (url, options = {}) => {\n  const { proxy = {}, signal = null } = options\n  const {\n    target,\n    headers = {},\n  } = proxy\n  const res = await cross_fetch__WEBPACK_IMPORTED_MODULE_0__(target + encodeURIComponent(url), {\n    headers,\n    signal,\n  })\n  return res\n}\n\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (async (url, options = {}) => {\n  const {\n    headers = {\n      'user-agent': 'Mozilla/5.0 (X11; Linux x86_64; rv:109.0) Gecko/20100101 Firefox/115.0',\n    },\n    proxy = null,\n    agent = null,\n    signal = null,\n  } = options\n\n  const res = proxy ? await profetch(url, { proxy, signal }) : await cross_fetch__WEBPACK_IMPORTED_MODULE_0__(url, { headers, agent, signal })\n\n  const status = res.status\n  if (status >= 400) {\n    throw new Error(`Request failed with error code ${status}`)\n  }\n  const buffer = await res.arrayBuffer()\n  return buffer\n});\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@extractus/article-extractor/src/utils/retrieve.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@extractus/article-extractor/src/utils/similarity.js":
/*!***************************************************************************!*\
  !*** ./node_modules/@extractus/article-extractor/src/utils/similarity.js ***!
  \***************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   compareTwoStrings: () => (/* binding */ compareTwoStrings),\n/* harmony export */   findBestMatch: () => (/* binding */ findBestMatch)\n/* harmony export */ });\n/* harmony import */ var _ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ndaidong/bellajs */ \"(rsc)/./node_modules/@ndaidong/bellajs/esm/mod.js\");\n// similarity.js\n// https://github.com/aceakash/string-similarity\n\n\n\nconst areArgsValid = (mainString, targetStrings) => {\n  return (0,_ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_0__.isString)(mainString) && (0,_ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_0__.isArray)(targetStrings)\n    && targetStrings.length > 0 && targetStrings.every(s => (0,_ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_0__.isString)(s))\n}\n\nconst compareTwoStrings = (first, second) => {\n  first = first.replace(/\\s+/g, '')\n  second = second.replace(/\\s+/g, '')\n\n  if (first === second) return 1 // identical or empty\n  if (first.length < 2 || second.length < 2) return 0 // if either is a 0-letter or 1-letter string\n\n  let firstBigrams = new Map()\n  for (let i = 0; i < first.length - 1; i++) {\n    const bigram = first.substring(i, i + 2)\n    const count = firstBigrams.has(bigram)\n      ? firstBigrams.get(bigram) + 1\n      : 1\n\n    firstBigrams.set(bigram, count)\n  }\n\n  let intersectionSize = 0\n  for (let i = 0; i < second.length - 1; i++) {\n    const bigram = second.substring(i, i + 2)\n    const count = firstBigrams.has(bigram)\n      ? firstBigrams.get(bigram)\n      : 0\n\n    if (count > 0) {\n      firstBigrams.set(bigram, count - 1)\n      intersectionSize++\n    }\n  }\n\n  return (2.0 * intersectionSize) / (first.length + second.length - 2)\n}\n\nconst findBestMatch = (mainString, targetStrings) => {\n  if (!areArgsValid(mainString, targetStrings)) {\n    throw new Error('Bad arguments: First argument should be a string, second should be an array of strings')\n  }\n\n  const ratings = []\n  let bestMatchIndex = 0\n\n  for (let i = 0; i < targetStrings.length; i++) {\n    const currentTargetString = targetStrings[i]\n    const currentRating = compareTwoStrings(mainString, currentTargetString)\n    ratings.push({ target: currentTargetString, rating: currentRating })\n    if (currentRating > ratings[bestMatchIndex].rating) {\n      bestMatchIndex = i\n    }\n  }\n\n  const bestMatch = ratings[bestMatchIndex]\n\n  return { ratings: ratings, bestMatch: bestMatch, bestMatchIndex: bestMatchIndex }\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@extractus/article-extractor/src/utils/similarity.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/@extractus/article-extractor/src/utils/transformation.js":
/*!*******************************************************************************!*\
  !*** ./node_modules/@extractus/article-extractor/src/utils/transformation.js ***!
  \*******************************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   addTransformations: () => (/* binding */ addTransformations),\n/* harmony export */   execPostParser: () => (/* binding */ execPostParser),\n/* harmony export */   execPreParser: () => (/* binding */ execPreParser),\n/* harmony export */   findTransformations: () => (/* binding */ findTransformations),\n/* harmony export */   getTransformations: () => (/* binding */ getTransformations),\n/* harmony export */   removeTransformations: () => (/* binding */ removeTransformations)\n/* harmony export */ });\n/* harmony import */ var _ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @ndaidong/bellajs */ \"(rsc)/./node_modules/@ndaidong/bellajs/esm/mod.js\");\n/* harmony import */ var linkedom__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! linkedom */ \"(rsc)/./node_modules/linkedom/esm/index.js\");\n// utils --> transformation.js\n\n\n\n\nconst transformations = []\n\nconst add = (tn) => {\n  const { patterns } = tn\n  if (!patterns || !(0,_ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_0__.isArray)(patterns) || !patterns.length) {\n    return 0\n  }\n  transformations.push(tn)\n  return 1\n}\n\nconst addTransformations = (tfms) => {\n  if ((0,_ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_0__.isArray)(tfms)) {\n    return tfms.map(tfm => add(tfm)).filter(result => result === 1).length\n  }\n  return add(tfms)\n}\n\nconst removeTransformations = (patterns) => {\n  if (!patterns) {\n    const removed = transformations.length\n    transformations.length = 0\n    return removed\n  }\n  let removing = 0\n  for (let i = transformations.length - 1; i > 0; i--) {\n    const { patterns: ipatterns } = transformations[i]\n    const matched = ipatterns.some((ptn) => patterns.some((pattern) => String(pattern) === String(ptn)))\n    if (matched) {\n      transformations.splice(i, 1)\n      removing += 1\n    }\n  }\n  return removing\n}\n\nconst getTransformations = () => {\n  return (0,_ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_0__.clone)(transformations)\n}\n\nconst findTransformations = (links) => {\n  const urls = !(0,_ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_0__.isArray)(links) ? [links] : links\n  const tfms = []\n  for (const transformation of transformations) {\n    const { patterns } = transformation\n    const matched = urls.some((url) => patterns.some((pattern) => pattern.test(url)))\n    if (matched) {\n      tfms.push((0,_ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_0__.clone)(transformation))\n    }\n  }\n  return tfms\n}\n\nconst execPreParser = (html, links) => {\n  const doc = new linkedom__WEBPACK_IMPORTED_MODULE_1__.DOMParser().parseFromString(html, 'text/html')\n  findTransformations(links).map(tfm => tfm.pre).filter(fn => (0,_ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_0__.isFunction)(fn)).map(fn => fn(doc))\n  return Array.from(doc.childNodes).map(it => it.outerHTML).join('')\n}\n\nconst execPostParser = (html, links) => {\n  const doc = new linkedom__WEBPACK_IMPORTED_MODULE_1__.DOMParser().parseFromString(html, 'text/html')\n  findTransformations(links).map(tfm => tfm.post).filter(fn => (0,_ndaidong_bellajs__WEBPACK_IMPORTED_MODULE_0__.isFunction)(fn)).map(fn => fn(doc))\n  return Array.from(doc.childNodes).map(it => it.outerHTML).join('')\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/@extractus/article-extractor/src/utils/transformation.js\n");

/***/ })

};
;