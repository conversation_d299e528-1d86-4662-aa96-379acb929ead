/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/xmlbuilder";
exports.ids = ["vendor-chunks/xmlbuilder"];
exports.modules = {

/***/ "(rsc)/./node_modules/xmlbuilder/lib/DocumentPosition.js":
/*!*********************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/DocumentPosition.js ***!
  \*********************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  module.exports = {\n    Disconnected: 1,\n    Preceding: 2,\n    Following: 4,\n    Contains: 8,\n    ContainedBy: 16,\n    ImplementationSpecific: 32\n  };\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMveG1sYnVpbGRlci9saWIvRG9jdW1lbnRQb3NpdGlvbi5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxDQUFDIiwic291cmNlcyI6WyIvVXNlcnMvc2FudGhvc2hwYWxhbmlzYW15L3Byb2plY3RzL0FnZW50RGV2ZWxvcG1lbnQvdHdpdHRlcmJvdC90d2l0dGVyLWJvdC1kYXNoYm9hcmQvbm9kZV9tb2R1bGVzL3htbGJ1aWxkZXIvbGliL0RvY3VtZW50UG9zaXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gR2VuZXJhdGVkIGJ5IENvZmZlZVNjcmlwdCAxLjEyLjdcbihmdW5jdGlvbigpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSB7XG4gICAgRGlzY29ubmVjdGVkOiAxLFxuICAgIFByZWNlZGluZzogMixcbiAgICBGb2xsb3dpbmc6IDQsXG4gICAgQ29udGFpbnM6IDgsXG4gICAgQ29udGFpbmVkQnk6IDE2LFxuICAgIEltcGxlbWVudGF0aW9uU3BlY2lmaWM6IDMyXG4gIH07XG5cbn0pLmNhbGwodGhpcyk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/DocumentPosition.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/NodeType.js":
/*!*************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/NodeType.js ***!
  \*************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  module.exports = {\n    Element: 1,\n    Attribute: 2,\n    Text: 3,\n    CData: 4,\n    EntityReference: 5,\n    EntityDeclaration: 6,\n    ProcessingInstruction: 7,\n    Comment: 8,\n    Document: 9,\n    DocType: 10,\n    DocumentFragment: 11,\n    NotationDeclaration: 12,\n    Declaration: 201,\n    Raw: 202,\n    AttributeDeclaration: 203,\n    ElementDeclaration: 204,\n    Dummy: 205\n  };\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMveG1sYnVpbGRlci9saWIvTm9kZVR5cGUuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUVBLENBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zYW50aG9zaHBhbGFuaXNhbXkvcHJvamVjdHMvQWdlbnREZXZlbG9wbWVudC90d2l0dGVyYm90L3R3aXR0ZXItYm90LWRhc2hib2FyZC9ub2RlX21vZHVsZXMveG1sYnVpbGRlci9saWIvTm9kZVR5cGUuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gR2VuZXJhdGVkIGJ5IENvZmZlZVNjcmlwdCAxLjEyLjdcbihmdW5jdGlvbigpIHtcbiAgbW9kdWxlLmV4cG9ydHMgPSB7XG4gICAgRWxlbWVudDogMSxcbiAgICBBdHRyaWJ1dGU6IDIsXG4gICAgVGV4dDogMyxcbiAgICBDRGF0YTogNCxcbiAgICBFbnRpdHlSZWZlcmVuY2U6IDUsXG4gICAgRW50aXR5RGVjbGFyYXRpb246IDYsXG4gICAgUHJvY2Vzc2luZ0luc3RydWN0aW9uOiA3LFxuICAgIENvbW1lbnQ6IDgsXG4gICAgRG9jdW1lbnQ6IDksXG4gICAgRG9jVHlwZTogMTAsXG4gICAgRG9jdW1lbnRGcmFnbWVudDogMTEsXG4gICAgTm90YXRpb25EZWNsYXJhdGlvbjogMTIsXG4gICAgRGVjbGFyYXRpb246IDIwMSxcbiAgICBSYXc6IDIwMixcbiAgICBBdHRyaWJ1dGVEZWNsYXJhdGlvbjogMjAzLFxuICAgIEVsZW1lbnREZWNsYXJhdGlvbjogMjA0LFxuICAgIER1bW15OiAyMDVcbiAgfTtcblxufSkuY2FsbCh0aGlzKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/Utility.js":
/*!************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/Utility.js ***!
  \************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var assign, getValue, isArray, isEmpty, isFunction, isObject, isPlainObject,\n    slice = [].slice,\n    hasProp = {}.hasOwnProperty;\n\n  assign = function() {\n    var i, key, len, source, sources, target;\n    target = arguments[0], sources = 2 <= arguments.length ? slice.call(arguments, 1) : [];\n    if (isFunction(Object.assign)) {\n      Object.assign.apply(null, arguments);\n    } else {\n      for (i = 0, len = sources.length; i < len; i++) {\n        source = sources[i];\n        if (source != null) {\n          for (key in source) {\n            if (!hasProp.call(source, key)) continue;\n            target[key] = source[key];\n          }\n        }\n      }\n    }\n    return target;\n  };\n\n  isFunction = function(val) {\n    return !!val && Object.prototype.toString.call(val) === '[object Function]';\n  };\n\n  isObject = function(val) {\n    var ref;\n    return !!val && ((ref = typeof val) === 'function' || ref === 'object');\n  };\n\n  isArray = function(val) {\n    if (isFunction(Array.isArray)) {\n      return Array.isArray(val);\n    } else {\n      return Object.prototype.toString.call(val) === '[object Array]';\n    }\n  };\n\n  isEmpty = function(val) {\n    var key;\n    if (isArray(val)) {\n      return !val.length;\n    } else {\n      for (key in val) {\n        if (!hasProp.call(val, key)) continue;\n        return false;\n      }\n      return true;\n    }\n  };\n\n  isPlainObject = function(val) {\n    var ctor, proto;\n    return isObject(val) && (proto = Object.getPrototypeOf(val)) && (ctor = proto.constructor) && (typeof ctor === 'function') && (ctor instanceof ctor) && (Function.prototype.toString.call(ctor) === Function.prototype.toString.call(Object));\n  };\n\n  getValue = function(obj) {\n    if (isFunction(obj.valueOf)) {\n      return obj.valueOf();\n    } else {\n      return obj;\n    }\n  };\n\n  module.exports.assign = assign;\n\n  module.exports.isFunction = isFunction;\n\n  module.exports.isObject = isObject;\n\n  module.exports.isArray = isArray;\n\n  module.exports.isEmpty = isEmpty;\n\n  module.exports.isPlainObject = isPlainObject;\n\n  module.exports.getValue = getValue;\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/Utility.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/WriterState.js":
/*!****************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/WriterState.js ***!
  \****************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  module.exports = {\n    None: 0,\n    OpenTag: 1,\n    InsideTag: 2,\n    CloseTag: 3\n  };\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMveG1sYnVpbGRlci9saWIvV3JpdGVyU3RhdGUuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7QUFFQSxDQUFDIiwic291cmNlcyI6WyIvVXNlcnMvc2FudGhvc2hwYWxhbmlzYW15L3Byb2plY3RzL0FnZW50RGV2ZWxvcG1lbnQvdHdpdHRlcmJvdC90d2l0dGVyLWJvdC1kYXNoYm9hcmQvbm9kZV9tb2R1bGVzL3htbGJ1aWxkZXIvbGliL1dyaXRlclN0YXRlLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEdlbmVyYXRlZCBieSBDb2ZmZWVTY3JpcHQgMS4xMi43XG4oZnVuY3Rpb24oKSB7XG4gIG1vZHVsZS5leHBvcnRzID0ge1xuICAgIE5vbmU6IDAsXG4gICAgT3BlblRhZzogMSxcbiAgICBJbnNpZGVUYWc6IDIsXG4gICAgQ2xvc2VUYWc6IDNcbiAgfTtcblxufSkuY2FsbCh0aGlzKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/WriterState.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLAttribute.js":
/*!*****************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLAttribute.js ***!
  \*****************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLAttribute, XMLNode;\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  module.exports = XMLAttribute = (function() {\n    function XMLAttribute(parent, name, value) {\n      this.parent = parent;\n      if (this.parent) {\n        this.options = this.parent.options;\n        this.stringify = this.parent.stringify;\n      }\n      if (name == null) {\n        throw new Error(\"Missing attribute name. \" + this.debugInfo(name));\n      }\n      this.name = this.stringify.name(name);\n      this.value = this.stringify.attValue(value);\n      this.type = NodeType.Attribute;\n      this.isId = false;\n      this.schemaTypeInfo = null;\n    }\n\n    Object.defineProperty(XMLAttribute.prototype, 'nodeType', {\n      get: function() {\n        return this.type;\n      }\n    });\n\n    Object.defineProperty(XMLAttribute.prototype, 'ownerElement', {\n      get: function() {\n        return this.parent;\n      }\n    });\n\n    Object.defineProperty(XMLAttribute.prototype, 'textContent', {\n      get: function() {\n        return this.value;\n      },\n      set: function(value) {\n        return this.value = value || '';\n      }\n    });\n\n    Object.defineProperty(XMLAttribute.prototype, 'namespaceURI', {\n      get: function() {\n        return '';\n      }\n    });\n\n    Object.defineProperty(XMLAttribute.prototype, 'prefix', {\n      get: function() {\n        return '';\n      }\n    });\n\n    Object.defineProperty(XMLAttribute.prototype, 'localName', {\n      get: function() {\n        return this.name;\n      }\n    });\n\n    Object.defineProperty(XMLAttribute.prototype, 'specified', {\n      get: function() {\n        return true;\n      }\n    });\n\n    XMLAttribute.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLAttribute.prototype.toString = function(options) {\n      return this.options.writer.attribute(this, this.options.writer.filterOptions(options));\n    };\n\n    XMLAttribute.prototype.debugInfo = function(name) {\n      name = name || this.name;\n      if (name == null) {\n        return \"parent: <\" + this.parent.name + \">\";\n      } else {\n        return \"attribute: {\" + name + \"}, parent: <\" + this.parent.name + \">\";\n      }\n    };\n\n    XMLAttribute.prototype.isEqualNode = function(node) {\n      if (node.namespaceURI !== this.namespaceURI) {\n        return false;\n      }\n      if (node.prefix !== this.prefix) {\n        return false;\n      }\n      if (node.localName !== this.localName) {\n        return false;\n      }\n      if (node.value !== this.value) {\n        return false;\n      }\n      return true;\n    };\n\n    return XMLAttribute;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLAttribute.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLCData.js":
/*!*************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLCData.js ***!
  \*************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLCData, XMLCharacterData,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLCharacterData = __webpack_require__(/*! ./XMLCharacterData */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLCharacterData.js\");\n\n  module.exports = XMLCData = (function(superClass) {\n    extend(XMLCData, superClass);\n\n    function XMLCData(parent, text) {\n      XMLCData.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing CDATA text. \" + this.debugInfo());\n      }\n      this.name = \"#cdata-section\";\n      this.type = NodeType.CData;\n      this.value = this.stringify.cdata(text);\n    }\n\n    XMLCData.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLCData.prototype.toString = function(options) {\n      return this.options.writer.cdata(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLCData;\n\n  })(XMLCharacterData);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLCData.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLCharacterData.js":
/*!*********************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLCharacterData.js ***!
  \*********************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLCharacterData, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  module.exports = XMLCharacterData = (function(superClass) {\n    extend(XMLCharacterData, superClass);\n\n    function XMLCharacterData(parent) {\n      XMLCharacterData.__super__.constructor.call(this, parent);\n      this.value = '';\n    }\n\n    Object.defineProperty(XMLCharacterData.prototype, 'data', {\n      get: function() {\n        return this.value;\n      },\n      set: function(value) {\n        return this.value = value || '';\n      }\n    });\n\n    Object.defineProperty(XMLCharacterData.prototype, 'length', {\n      get: function() {\n        return this.value.length;\n      }\n    });\n\n    Object.defineProperty(XMLCharacterData.prototype, 'textContent', {\n      get: function() {\n        return this.value;\n      },\n      set: function(value) {\n        return this.value = value || '';\n      }\n    });\n\n    XMLCharacterData.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLCharacterData.prototype.substringData = function(offset, count) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLCharacterData.prototype.appendData = function(arg) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLCharacterData.prototype.insertData = function(offset, arg) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLCharacterData.prototype.deleteData = function(offset, count) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLCharacterData.prototype.replaceData = function(offset, count, arg) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLCharacterData.prototype.isEqualNode = function(node) {\n      if (!XMLCharacterData.__super__.isEqualNode.apply(this, arguments).isEqualNode(node)) {\n        return false;\n      }\n      if (node.data !== this.data) {\n        return false;\n      }\n      return true;\n    };\n\n    return XMLCharacterData;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLCharacterData.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLComment.js":
/*!***************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLComment.js ***!
  \***************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLCharacterData, XMLComment,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLCharacterData = __webpack_require__(/*! ./XMLCharacterData */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLCharacterData.js\");\n\n  module.exports = XMLComment = (function(superClass) {\n    extend(XMLComment, superClass);\n\n    function XMLComment(parent, text) {\n      XMLComment.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing comment text. \" + this.debugInfo());\n      }\n      this.name = \"#comment\";\n      this.type = NodeType.Comment;\n      this.value = this.stringify.comment(text);\n    }\n\n    XMLComment.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLComment.prototype.toString = function(options) {\n      return this.options.writer.comment(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLComment;\n\n  })(XMLCharacterData);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLComment.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLDOMConfiguration.js":
/*!************************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDOMConfiguration.js ***!
  \************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDOMConfiguration, XMLDOMErrorHandler, XMLDOMStringList;\n\n  XMLDOMErrorHandler = __webpack_require__(/*! ./XMLDOMErrorHandler */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDOMErrorHandler.js\");\n\n  XMLDOMStringList = __webpack_require__(/*! ./XMLDOMStringList */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDOMStringList.js\");\n\n  module.exports = XMLDOMConfiguration = (function() {\n    function XMLDOMConfiguration() {\n      var clonedSelf;\n      this.defaultParams = {\n        \"canonical-form\": false,\n        \"cdata-sections\": false,\n        \"comments\": false,\n        \"datatype-normalization\": false,\n        \"element-content-whitespace\": true,\n        \"entities\": true,\n        \"error-handler\": new XMLDOMErrorHandler(),\n        \"infoset\": true,\n        \"validate-if-schema\": false,\n        \"namespaces\": true,\n        \"namespace-declarations\": true,\n        \"normalize-characters\": false,\n        \"schema-location\": '',\n        \"schema-type\": '',\n        \"split-cdata-sections\": true,\n        \"validate\": false,\n        \"well-formed\": true\n      };\n      this.params = clonedSelf = Object.create(this.defaultParams);\n    }\n\n    Object.defineProperty(XMLDOMConfiguration.prototype, 'parameterNames', {\n      get: function() {\n        return new XMLDOMStringList(Object.keys(this.defaultParams));\n      }\n    });\n\n    XMLDOMConfiguration.prototype.getParameter = function(name) {\n      if (this.params.hasOwnProperty(name)) {\n        return this.params[name];\n      } else {\n        return null;\n      }\n    };\n\n    XMLDOMConfiguration.prototype.canSetParameter = function(name, value) {\n      return true;\n    };\n\n    XMLDOMConfiguration.prototype.setParameter = function(name, value) {\n      if (value != null) {\n        return this.params[name] = value;\n      } else {\n        return delete this.params[name];\n      }\n    };\n\n    return XMLDOMConfiguration;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLDOMConfiguration.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLDOMErrorHandler.js":
/*!***********************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDOMErrorHandler.js ***!
  \***********************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDOMErrorHandler;\n\n  module.exports = XMLDOMErrorHandler = (function() {\n    function XMLDOMErrorHandler() {}\n\n    XMLDOMErrorHandler.prototype.handleError = function(error) {\n      throw new Error(error);\n    };\n\n    return XMLDOMErrorHandler;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMveG1sYnVpbGRlci9saWIvWE1MRE9NRXJyb3JIYW5kbGVyLmpzIiwibWFwcGluZ3MiOiJBQUFBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQSxHQUFHOztBQUVILENBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zYW50aG9zaHBhbGFuaXNhbXkvcHJvamVjdHMvQWdlbnREZXZlbG9wbWVudC90d2l0dGVyYm90L3R3aXR0ZXItYm90LWRhc2hib2FyZC9ub2RlX21vZHVsZXMveG1sYnVpbGRlci9saWIvWE1MRE9NRXJyb3JIYW5kbGVyLmpzIl0sInNvdXJjZXNDb250ZW50IjpbIi8vIEdlbmVyYXRlZCBieSBDb2ZmZWVTY3JpcHQgMS4xMi43XG4oZnVuY3Rpb24oKSB7XG4gIHZhciBYTUxET01FcnJvckhhbmRsZXI7XG5cbiAgbW9kdWxlLmV4cG9ydHMgPSBYTUxET01FcnJvckhhbmRsZXIgPSAoZnVuY3Rpb24oKSB7XG4gICAgZnVuY3Rpb24gWE1MRE9NRXJyb3JIYW5kbGVyKCkge31cblxuICAgIFhNTERPTUVycm9ySGFuZGxlci5wcm90b3R5cGUuaGFuZGxlRXJyb3IgPSBmdW5jdGlvbihlcnJvcikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yKTtcbiAgICB9O1xuXG4gICAgcmV0dXJuIFhNTERPTUVycm9ySGFuZGxlcjtcblxuICB9KSgpO1xuXG59KS5jYWxsKHRoaXMpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLDOMErrorHandler.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLDOMImplementation.js":
/*!*************************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDOMImplementation.js ***!
  \*************************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDOMImplementation;\n\n  module.exports = XMLDOMImplementation = (function() {\n    function XMLDOMImplementation() {}\n\n    XMLDOMImplementation.prototype.hasFeature = function(feature, version) {\n      return true;\n    };\n\n    XMLDOMImplementation.prototype.createDocumentType = function(qualifiedName, publicId, systemId) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n\n    XMLDOMImplementation.prototype.createDocument = function(namespaceURI, qualifiedName, doctype) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n\n    XMLDOMImplementation.prototype.createHTMLDocument = function(title) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n\n    XMLDOMImplementation.prototype.getFeature = function(feature, version) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n\n    return XMLDOMImplementation;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMveG1sYnVpbGRlci9saWIvWE1MRE9NSW1wbGVtZW50YXRpb24uanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQSxHQUFHOztBQUVILENBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zYW50aG9zaHBhbGFuaXNhbXkvcHJvamVjdHMvQWdlbnREZXZlbG9wbWVudC90d2l0dGVyYm90L3R3aXR0ZXItYm90LWRhc2hib2FyZC9ub2RlX21vZHVsZXMveG1sYnVpbGRlci9saWIvWE1MRE9NSW1wbGVtZW50YXRpb24uanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gR2VuZXJhdGVkIGJ5IENvZmZlZVNjcmlwdCAxLjEyLjdcbihmdW5jdGlvbigpIHtcbiAgdmFyIFhNTERPTUltcGxlbWVudGF0aW9uO1xuXG4gIG1vZHVsZS5leHBvcnRzID0gWE1MRE9NSW1wbGVtZW50YXRpb24gPSAoZnVuY3Rpb24oKSB7XG4gICAgZnVuY3Rpb24gWE1MRE9NSW1wbGVtZW50YXRpb24oKSB7fVxuXG4gICAgWE1MRE9NSW1wbGVtZW50YXRpb24ucHJvdG90eXBlLmhhc0ZlYXR1cmUgPSBmdW5jdGlvbihmZWF0dXJlLCB2ZXJzaW9uKSB7XG4gICAgICByZXR1cm4gdHJ1ZTtcbiAgICB9O1xuXG4gICAgWE1MRE9NSW1wbGVtZW50YXRpb24ucHJvdG90eXBlLmNyZWF0ZURvY3VtZW50VHlwZSA9IGZ1bmN0aW9uKHF1YWxpZmllZE5hbWUsIHB1YmxpY0lkLCBzeXN0ZW1JZCkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFwiVGhpcyBET00gbWV0aG9kIGlzIG5vdCBpbXBsZW1lbnRlZC5cIik7XG4gICAgfTtcblxuICAgIFhNTERPTUltcGxlbWVudGF0aW9uLnByb3RvdHlwZS5jcmVhdGVEb2N1bWVudCA9IGZ1bmN0aW9uKG5hbWVzcGFjZVVSSSwgcXVhbGlmaWVkTmFtZSwgZG9jdHlwZSkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFwiVGhpcyBET00gbWV0aG9kIGlzIG5vdCBpbXBsZW1lbnRlZC5cIik7XG4gICAgfTtcblxuICAgIFhNTERPTUltcGxlbWVudGF0aW9uLnByb3RvdHlwZS5jcmVhdGVIVE1MRG9jdW1lbnQgPSBmdW5jdGlvbih0aXRsZSkge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFwiVGhpcyBET00gbWV0aG9kIGlzIG5vdCBpbXBsZW1lbnRlZC5cIik7XG4gICAgfTtcblxuICAgIFhNTERPTUltcGxlbWVudGF0aW9uLnByb3RvdHlwZS5nZXRGZWF0dXJlID0gZnVuY3Rpb24oZmVhdHVyZSwgdmVyc2lvbikge1xuICAgICAgdGhyb3cgbmV3IEVycm9yKFwiVGhpcyBET00gbWV0aG9kIGlzIG5vdCBpbXBsZW1lbnRlZC5cIik7XG4gICAgfTtcblxuICAgIHJldHVybiBYTUxET01JbXBsZW1lbnRhdGlvbjtcblxuICB9KSgpO1xuXG59KS5jYWxsKHRoaXMpO1xuIl0sIm5hbWVzIjpbXSwiaWdub3JlTGlzdCI6WzBdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLDOMImplementation.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLDOMStringList.js":
/*!*********************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDOMStringList.js ***!
  \*********************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLDOMStringList;\n\n  module.exports = XMLDOMStringList = (function() {\n    function XMLDOMStringList(arr) {\n      this.arr = arr || [];\n    }\n\n    Object.defineProperty(XMLDOMStringList.prototype, 'length', {\n      get: function() {\n        return this.arr.length;\n      }\n    });\n\n    XMLDOMStringList.prototype.item = function(index) {\n      return this.arr[index] || null;\n    };\n\n    XMLDOMStringList.prototype.contains = function(str) {\n      return this.arr.indexOf(str) !== -1;\n    };\n\n    return XMLDOMStringList;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMveG1sYnVpbGRlci9saWIvWE1MRE9NU3RyaW5nTGlzdC5qcyIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQSxLQUFLOztBQUVMO0FBQ0E7QUFDQTs7QUFFQTtBQUNBO0FBQ0E7O0FBRUE7O0FBRUEsR0FBRzs7QUFFSCxDQUFDIiwic291cmNlcyI6WyIvVXNlcnMvc2FudGhvc2hwYWxhbmlzYW15L3Byb2plY3RzL0FnZW50RGV2ZWxvcG1lbnQvdHdpdHRlcmJvdC90d2l0dGVyLWJvdC1kYXNoYm9hcmQvbm9kZV9tb2R1bGVzL3htbGJ1aWxkZXIvbGliL1hNTERPTVN0cmluZ0xpc3QuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gR2VuZXJhdGVkIGJ5IENvZmZlZVNjcmlwdCAxLjEyLjdcbihmdW5jdGlvbigpIHtcbiAgdmFyIFhNTERPTVN0cmluZ0xpc3Q7XG5cbiAgbW9kdWxlLmV4cG9ydHMgPSBYTUxET01TdHJpbmdMaXN0ID0gKGZ1bmN0aW9uKCkge1xuICAgIGZ1bmN0aW9uIFhNTERPTVN0cmluZ0xpc3QoYXJyKSB7XG4gICAgICB0aGlzLmFyciA9IGFyciB8fCBbXTtcbiAgICB9XG5cbiAgICBPYmplY3QuZGVmaW5lUHJvcGVydHkoWE1MRE9NU3RyaW5nTGlzdC5wcm90b3R5cGUsICdsZW5ndGgnLCB7XG4gICAgICBnZXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5hcnIubGVuZ3RoO1xuICAgICAgfVxuICAgIH0pO1xuXG4gICAgWE1MRE9NU3RyaW5nTGlzdC5wcm90b3R5cGUuaXRlbSA9IGZ1bmN0aW9uKGluZGV4KSB7XG4gICAgICByZXR1cm4gdGhpcy5hcnJbaW5kZXhdIHx8IG51bGw7XG4gICAgfTtcblxuICAgIFhNTERPTVN0cmluZ0xpc3QucHJvdG90eXBlLmNvbnRhaW5zID0gZnVuY3Rpb24oc3RyKSB7XG4gICAgICByZXR1cm4gdGhpcy5hcnIuaW5kZXhPZihzdHIpICE9PSAtMTtcbiAgICB9O1xuXG4gICAgcmV0dXJuIFhNTERPTVN0cmluZ0xpc3Q7XG5cbiAgfSkoKTtcblxufSkuY2FsbCh0aGlzKTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLDOMStringList.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLDTDAttList.js":
/*!******************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDTDAttList.js ***!
  \******************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLDTDAttList, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  module.exports = XMLDTDAttList = (function(superClass) {\n    extend(XMLDTDAttList, superClass);\n\n    function XMLDTDAttList(parent, elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n      XMLDTDAttList.__super__.constructor.call(this, parent);\n      if (elementName == null) {\n        throw new Error(\"Missing DTD element name. \" + this.debugInfo());\n      }\n      if (attributeName == null) {\n        throw new Error(\"Missing DTD attribute name. \" + this.debugInfo(elementName));\n      }\n      if (!attributeType) {\n        throw new Error(\"Missing DTD attribute type. \" + this.debugInfo(elementName));\n      }\n      if (!defaultValueType) {\n        throw new Error(\"Missing DTD attribute default. \" + this.debugInfo(elementName));\n      }\n      if (defaultValueType.indexOf('#') !== 0) {\n        defaultValueType = '#' + defaultValueType;\n      }\n      if (!defaultValueType.match(/^(#REQUIRED|#IMPLIED|#FIXED|#DEFAULT)$/)) {\n        throw new Error(\"Invalid default value type; expected: #REQUIRED, #IMPLIED, #FIXED or #DEFAULT. \" + this.debugInfo(elementName));\n      }\n      if (defaultValue && !defaultValueType.match(/^(#FIXED|#DEFAULT)$/)) {\n        throw new Error(\"Default value only applies to #FIXED or #DEFAULT. \" + this.debugInfo(elementName));\n      }\n      this.elementName = this.stringify.name(elementName);\n      this.type = NodeType.AttributeDeclaration;\n      this.attributeName = this.stringify.name(attributeName);\n      this.attributeType = this.stringify.dtdAttType(attributeType);\n      if (defaultValue) {\n        this.defaultValue = this.stringify.dtdAttDefault(defaultValue);\n      }\n      this.defaultValueType = defaultValueType;\n    }\n\n    XMLDTDAttList.prototype.toString = function(options) {\n      return this.options.writer.dtdAttList(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLDTDAttList;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLDTDAttList.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLDTDElement.js":
/*!******************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDTDElement.js ***!
  \******************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLDTDElement, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  module.exports = XMLDTDElement = (function(superClass) {\n    extend(XMLDTDElement, superClass);\n\n    function XMLDTDElement(parent, name, value) {\n      XMLDTDElement.__super__.constructor.call(this, parent);\n      if (name == null) {\n        throw new Error(\"Missing DTD element name. \" + this.debugInfo());\n      }\n      if (!value) {\n        value = '(#PCDATA)';\n      }\n      if (Array.isArray(value)) {\n        value = '(' + value.join(',') + ')';\n      }\n      this.name = this.stringify.name(name);\n      this.type = NodeType.ElementDeclaration;\n      this.value = this.stringify.dtdElementValue(value);\n    }\n\n    XMLDTDElement.prototype.toString = function(options) {\n      return this.options.writer.dtdElement(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLDTDElement;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLDTDElement.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLDTDEntity.js":
/*!*****************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDTDEntity.js ***!
  \*****************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLDTDEntity, XMLNode, isObject,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  isObject = (__webpack_require__(/*! ./Utility */ \"(rsc)/./node_modules/xmlbuilder/lib/Utility.js\").isObject);\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  module.exports = XMLDTDEntity = (function(superClass) {\n    extend(XMLDTDEntity, superClass);\n\n    function XMLDTDEntity(parent, pe, name, value) {\n      XMLDTDEntity.__super__.constructor.call(this, parent);\n      if (name == null) {\n        throw new Error(\"Missing DTD entity name. \" + this.debugInfo(name));\n      }\n      if (value == null) {\n        throw new Error(\"Missing DTD entity value. \" + this.debugInfo(name));\n      }\n      this.pe = !!pe;\n      this.name = this.stringify.name(name);\n      this.type = NodeType.EntityDeclaration;\n      if (!isObject(value)) {\n        this.value = this.stringify.dtdEntityValue(value);\n        this.internal = true;\n      } else {\n        if (!value.pubID && !value.sysID) {\n          throw new Error(\"Public and/or system identifiers are required for an external entity. \" + this.debugInfo(name));\n        }\n        if (value.pubID && !value.sysID) {\n          throw new Error(\"System identifier is required for a public external entity. \" + this.debugInfo(name));\n        }\n        this.internal = false;\n        if (value.pubID != null) {\n          this.pubID = this.stringify.dtdPubID(value.pubID);\n        }\n        if (value.sysID != null) {\n          this.sysID = this.stringify.dtdSysID(value.sysID);\n        }\n        if (value.nData != null) {\n          this.nData = this.stringify.dtdNData(value.nData);\n        }\n        if (this.pe && this.nData) {\n          throw new Error(\"Notation declaration is not allowed in a parameter entity. \" + this.debugInfo(name));\n        }\n      }\n    }\n\n    Object.defineProperty(XMLDTDEntity.prototype, 'publicId', {\n      get: function() {\n        return this.pubID;\n      }\n    });\n\n    Object.defineProperty(XMLDTDEntity.prototype, 'systemId', {\n      get: function() {\n        return this.sysID;\n      }\n    });\n\n    Object.defineProperty(XMLDTDEntity.prototype, 'notationName', {\n      get: function() {\n        return this.nData || null;\n      }\n    });\n\n    Object.defineProperty(XMLDTDEntity.prototype, 'inputEncoding', {\n      get: function() {\n        return null;\n      }\n    });\n\n    Object.defineProperty(XMLDTDEntity.prototype, 'xmlEncoding', {\n      get: function() {\n        return null;\n      }\n    });\n\n    Object.defineProperty(XMLDTDEntity.prototype, 'xmlVersion', {\n      get: function() {\n        return null;\n      }\n    });\n\n    XMLDTDEntity.prototype.toString = function(options) {\n      return this.options.writer.dtdEntity(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLDTDEntity;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLDTDEntity.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLDTDNotation.js":
/*!*******************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDTDNotation.js ***!
  \*******************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLDTDNotation, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  module.exports = XMLDTDNotation = (function(superClass) {\n    extend(XMLDTDNotation, superClass);\n\n    function XMLDTDNotation(parent, name, value) {\n      XMLDTDNotation.__super__.constructor.call(this, parent);\n      if (name == null) {\n        throw new Error(\"Missing DTD notation name. \" + this.debugInfo(name));\n      }\n      if (!value.pubID && !value.sysID) {\n        throw new Error(\"Public or system identifiers are required for an external entity. \" + this.debugInfo(name));\n      }\n      this.name = this.stringify.name(name);\n      this.type = NodeType.NotationDeclaration;\n      if (value.pubID != null) {\n        this.pubID = this.stringify.dtdPubID(value.pubID);\n      }\n      if (value.sysID != null) {\n        this.sysID = this.stringify.dtdSysID(value.sysID);\n      }\n    }\n\n    Object.defineProperty(XMLDTDNotation.prototype, 'publicId', {\n      get: function() {\n        return this.pubID;\n      }\n    });\n\n    Object.defineProperty(XMLDTDNotation.prototype, 'systemId', {\n      get: function() {\n        return this.sysID;\n      }\n    });\n\n    XMLDTDNotation.prototype.toString = function(options) {\n      return this.options.writer.dtdNotation(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLDTDNotation;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLDTDNotation.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLDeclaration.js":
/*!*******************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDeclaration.js ***!
  \*******************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLDeclaration, XMLNode, isObject,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  isObject = (__webpack_require__(/*! ./Utility */ \"(rsc)/./node_modules/xmlbuilder/lib/Utility.js\").isObject);\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  module.exports = XMLDeclaration = (function(superClass) {\n    extend(XMLDeclaration, superClass);\n\n    function XMLDeclaration(parent, version, encoding, standalone) {\n      var ref;\n      XMLDeclaration.__super__.constructor.call(this, parent);\n      if (isObject(version)) {\n        ref = version, version = ref.version, encoding = ref.encoding, standalone = ref.standalone;\n      }\n      if (!version) {\n        version = '1.0';\n      }\n      this.type = NodeType.Declaration;\n      this.version = this.stringify.xmlVersion(version);\n      if (encoding != null) {\n        this.encoding = this.stringify.xmlEncoding(encoding);\n      }\n      if (standalone != null) {\n        this.standalone = this.stringify.xmlStandalone(standalone);\n      }\n    }\n\n    XMLDeclaration.prototype.toString = function(options) {\n      return this.options.writer.declaration(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLDeclaration;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLDeclaration.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLDocType.js":
/*!***************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDocType.js ***!
  \***************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLDTDAttList, XMLDTDElement, XMLDTDEntity, XMLDTDNotation, XMLDocType, XMLNamedNodeMap, XMLNode, isObject,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  isObject = (__webpack_require__(/*! ./Utility */ \"(rsc)/./node_modules/xmlbuilder/lib/Utility.js\").isObject);\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLDTDAttList = __webpack_require__(/*! ./XMLDTDAttList */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDTDAttList.js\");\n\n  XMLDTDEntity = __webpack_require__(/*! ./XMLDTDEntity */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDTDEntity.js\");\n\n  XMLDTDElement = __webpack_require__(/*! ./XMLDTDElement */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDTDElement.js\");\n\n  XMLDTDNotation = __webpack_require__(/*! ./XMLDTDNotation */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDTDNotation.js\");\n\n  XMLNamedNodeMap = __webpack_require__(/*! ./XMLNamedNodeMap */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNamedNodeMap.js\");\n\n  module.exports = XMLDocType = (function(superClass) {\n    extend(XMLDocType, superClass);\n\n    function XMLDocType(parent, pubID, sysID) {\n      var child, i, len, ref, ref1, ref2;\n      XMLDocType.__super__.constructor.call(this, parent);\n      this.type = NodeType.DocType;\n      if (parent.children) {\n        ref = parent.children;\n        for (i = 0, len = ref.length; i < len; i++) {\n          child = ref[i];\n          if (child.type === NodeType.Element) {\n            this.name = child.name;\n            break;\n          }\n        }\n      }\n      this.documentObject = parent;\n      if (isObject(pubID)) {\n        ref1 = pubID, pubID = ref1.pubID, sysID = ref1.sysID;\n      }\n      if (sysID == null) {\n        ref2 = [pubID, sysID], sysID = ref2[0], pubID = ref2[1];\n      }\n      if (pubID != null) {\n        this.pubID = this.stringify.dtdPubID(pubID);\n      }\n      if (sysID != null) {\n        this.sysID = this.stringify.dtdSysID(sysID);\n      }\n    }\n\n    Object.defineProperty(XMLDocType.prototype, 'entities', {\n      get: function() {\n        var child, i, len, nodes, ref;\n        nodes = {};\n        ref = this.children;\n        for (i = 0, len = ref.length; i < len; i++) {\n          child = ref[i];\n          if ((child.type === NodeType.EntityDeclaration) && !child.pe) {\n            nodes[child.name] = child;\n          }\n        }\n        return new XMLNamedNodeMap(nodes);\n      }\n    });\n\n    Object.defineProperty(XMLDocType.prototype, 'notations', {\n      get: function() {\n        var child, i, len, nodes, ref;\n        nodes = {};\n        ref = this.children;\n        for (i = 0, len = ref.length; i < len; i++) {\n          child = ref[i];\n          if (child.type === NodeType.NotationDeclaration) {\n            nodes[child.name] = child;\n          }\n        }\n        return new XMLNamedNodeMap(nodes);\n      }\n    });\n\n    Object.defineProperty(XMLDocType.prototype, 'publicId', {\n      get: function() {\n        return this.pubID;\n      }\n    });\n\n    Object.defineProperty(XMLDocType.prototype, 'systemId', {\n      get: function() {\n        return this.sysID;\n      }\n    });\n\n    Object.defineProperty(XMLDocType.prototype, 'internalSubset', {\n      get: function() {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n    });\n\n    XMLDocType.prototype.element = function(name, value) {\n      var child;\n      child = new XMLDTDElement(this, name, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLDocType.prototype.attList = function(elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n      var child;\n      child = new XMLDTDAttList(this, elementName, attributeName, attributeType, defaultValueType, defaultValue);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLDocType.prototype.entity = function(name, value) {\n      var child;\n      child = new XMLDTDEntity(this, false, name, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLDocType.prototype.pEntity = function(name, value) {\n      var child;\n      child = new XMLDTDEntity(this, true, name, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLDocType.prototype.notation = function(name, value) {\n      var child;\n      child = new XMLDTDNotation(this, name, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLDocType.prototype.toString = function(options) {\n      return this.options.writer.docType(this, this.options.writer.filterOptions(options));\n    };\n\n    XMLDocType.prototype.ele = function(name, value) {\n      return this.element(name, value);\n    };\n\n    XMLDocType.prototype.att = function(elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n      return this.attList(elementName, attributeName, attributeType, defaultValueType, defaultValue);\n    };\n\n    XMLDocType.prototype.ent = function(name, value) {\n      return this.entity(name, value);\n    };\n\n    XMLDocType.prototype.pent = function(name, value) {\n      return this.pEntity(name, value);\n    };\n\n    XMLDocType.prototype.not = function(name, value) {\n      return this.notation(name, value);\n    };\n\n    XMLDocType.prototype.up = function() {\n      return this.root() || this.documentObject;\n    };\n\n    XMLDocType.prototype.isEqualNode = function(node) {\n      if (!XMLDocType.__super__.isEqualNode.apply(this, arguments).isEqualNode(node)) {\n        return false;\n      }\n      if (node.name !== this.name) {\n        return false;\n      }\n      if (node.publicId !== this.publicId) {\n        return false;\n      }\n      if (node.systemId !== this.systemId) {\n        return false;\n      }\n      return true;\n    };\n\n    return XMLDocType;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLDocType.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLDocument.js":
/*!****************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDocument.js ***!
  \****************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLDOMConfiguration, XMLDOMImplementation, XMLDocument, XMLNode, XMLStringWriter, XMLStringifier, isPlainObject,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  isPlainObject = (__webpack_require__(/*! ./Utility */ \"(rsc)/./node_modules/xmlbuilder/lib/Utility.js\").isPlainObject);\n\n  XMLDOMImplementation = __webpack_require__(/*! ./XMLDOMImplementation */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDOMImplementation.js\");\n\n  XMLDOMConfiguration = __webpack_require__(/*! ./XMLDOMConfiguration */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDOMConfiguration.js\");\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLStringifier = __webpack_require__(/*! ./XMLStringifier */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLStringifier.js\");\n\n  XMLStringWriter = __webpack_require__(/*! ./XMLStringWriter */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLStringWriter.js\");\n\n  module.exports = XMLDocument = (function(superClass) {\n    extend(XMLDocument, superClass);\n\n    function XMLDocument(options) {\n      XMLDocument.__super__.constructor.call(this, null);\n      this.name = \"#document\";\n      this.type = NodeType.Document;\n      this.documentURI = null;\n      this.domConfig = new XMLDOMConfiguration();\n      options || (options = {});\n      if (!options.writer) {\n        options.writer = new XMLStringWriter();\n      }\n      this.options = options;\n      this.stringify = new XMLStringifier(options);\n    }\n\n    Object.defineProperty(XMLDocument.prototype, 'implementation', {\n      value: new XMLDOMImplementation()\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'doctype', {\n      get: function() {\n        var child, i, len, ref;\n        ref = this.children;\n        for (i = 0, len = ref.length; i < len; i++) {\n          child = ref[i];\n          if (child.type === NodeType.DocType) {\n            return child;\n          }\n        }\n        return null;\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'documentElement', {\n      get: function() {\n        return this.rootObject || null;\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'inputEncoding', {\n      get: function() {\n        return null;\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'strictErrorChecking', {\n      get: function() {\n        return false;\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'xmlEncoding', {\n      get: function() {\n        if (this.children.length !== 0 && this.children[0].type === NodeType.Declaration) {\n          return this.children[0].encoding;\n        } else {\n          return null;\n        }\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'xmlStandalone', {\n      get: function() {\n        if (this.children.length !== 0 && this.children[0].type === NodeType.Declaration) {\n          return this.children[0].standalone === 'yes';\n        } else {\n          return false;\n        }\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'xmlVersion', {\n      get: function() {\n        if (this.children.length !== 0 && this.children[0].type === NodeType.Declaration) {\n          return this.children[0].version;\n        } else {\n          return \"1.0\";\n        }\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'URL', {\n      get: function() {\n        return this.documentURI;\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'origin', {\n      get: function() {\n        return null;\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'compatMode', {\n      get: function() {\n        return null;\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'characterSet', {\n      get: function() {\n        return null;\n      }\n    });\n\n    Object.defineProperty(XMLDocument.prototype, 'contentType', {\n      get: function() {\n        return null;\n      }\n    });\n\n    XMLDocument.prototype.end = function(writer) {\n      var writerOptions;\n      writerOptions = {};\n      if (!writer) {\n        writer = this.options.writer;\n      } else if (isPlainObject(writer)) {\n        writerOptions = writer;\n        writer = this.options.writer;\n      }\n      return writer.document(this, writer.filterOptions(writerOptions));\n    };\n\n    XMLDocument.prototype.toString = function(options) {\n      return this.options.writer.document(this, this.options.writer.filterOptions(options));\n    };\n\n    XMLDocument.prototype.createElement = function(tagName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createDocumentFragment = function() {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createTextNode = function(data) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createComment = function(data) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createCDATASection = function(data) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createProcessingInstruction = function(target, data) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createAttribute = function(name) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createEntityReference = function(name) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.getElementsByTagName = function(tagname) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.importNode = function(importedNode, deep) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createElementNS = function(namespaceURI, qualifiedName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createAttributeNS = function(namespaceURI, qualifiedName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.getElementsByTagNameNS = function(namespaceURI, localName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.getElementById = function(elementId) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.adoptNode = function(source) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.normalizeDocument = function() {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.renameNode = function(node, namespaceURI, qualifiedName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.getElementsByClassName = function(classNames) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createEvent = function(eventInterface) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createRange = function() {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createNodeIterator = function(root, whatToShow, filter) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLDocument.prototype.createTreeWalker = function(root, whatToShow, filter) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    return XMLDocument;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLDocument.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLDocumentCB.js":
/*!******************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDocumentCB.js ***!
  \******************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, WriterState, XMLAttribute, XMLCData, XMLComment, XMLDTDAttList, XMLDTDElement, XMLDTDEntity, XMLDTDNotation, XMLDeclaration, XMLDocType, XMLDocument, XMLDocumentCB, XMLElement, XMLProcessingInstruction, XMLRaw, XMLStringWriter, XMLStringifier, XMLText, getValue, isFunction, isObject, isPlainObject, ref,\n    hasProp = {}.hasOwnProperty;\n\n  ref = __webpack_require__(/*! ./Utility */ \"(rsc)/./node_modules/xmlbuilder/lib/Utility.js\"), isObject = ref.isObject, isFunction = ref.isFunction, isPlainObject = ref.isPlainObject, getValue = ref.getValue;\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLDocument = __webpack_require__(/*! ./XMLDocument */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDocument.js\");\n\n  XMLElement = __webpack_require__(/*! ./XMLElement */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLElement.js\");\n\n  XMLCData = __webpack_require__(/*! ./XMLCData */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLCData.js\");\n\n  XMLComment = __webpack_require__(/*! ./XMLComment */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLComment.js\");\n\n  XMLRaw = __webpack_require__(/*! ./XMLRaw */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLRaw.js\");\n\n  XMLText = __webpack_require__(/*! ./XMLText */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLText.js\");\n\n  XMLProcessingInstruction = __webpack_require__(/*! ./XMLProcessingInstruction */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLProcessingInstruction.js\");\n\n  XMLDeclaration = __webpack_require__(/*! ./XMLDeclaration */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDeclaration.js\");\n\n  XMLDocType = __webpack_require__(/*! ./XMLDocType */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDocType.js\");\n\n  XMLDTDAttList = __webpack_require__(/*! ./XMLDTDAttList */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDTDAttList.js\");\n\n  XMLDTDEntity = __webpack_require__(/*! ./XMLDTDEntity */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDTDEntity.js\");\n\n  XMLDTDElement = __webpack_require__(/*! ./XMLDTDElement */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDTDElement.js\");\n\n  XMLDTDNotation = __webpack_require__(/*! ./XMLDTDNotation */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDTDNotation.js\");\n\n  XMLAttribute = __webpack_require__(/*! ./XMLAttribute */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLAttribute.js\");\n\n  XMLStringifier = __webpack_require__(/*! ./XMLStringifier */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLStringifier.js\");\n\n  XMLStringWriter = __webpack_require__(/*! ./XMLStringWriter */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLStringWriter.js\");\n\n  WriterState = __webpack_require__(/*! ./WriterState */ \"(rsc)/./node_modules/xmlbuilder/lib/WriterState.js\");\n\n  module.exports = XMLDocumentCB = (function() {\n    function XMLDocumentCB(options, onData, onEnd) {\n      var writerOptions;\n      this.name = \"?xml\";\n      this.type = NodeType.Document;\n      options || (options = {});\n      writerOptions = {};\n      if (!options.writer) {\n        options.writer = new XMLStringWriter();\n      } else if (isPlainObject(options.writer)) {\n        writerOptions = options.writer;\n        options.writer = new XMLStringWriter();\n      }\n      this.options = options;\n      this.writer = options.writer;\n      this.writerOptions = this.writer.filterOptions(writerOptions);\n      this.stringify = new XMLStringifier(options);\n      this.onDataCallback = onData || function() {};\n      this.onEndCallback = onEnd || function() {};\n      this.currentNode = null;\n      this.currentLevel = -1;\n      this.openTags = {};\n      this.documentStarted = false;\n      this.documentCompleted = false;\n      this.root = null;\n    }\n\n    XMLDocumentCB.prototype.createChildNode = function(node) {\n      var att, attName, attributes, child, i, len, ref1, ref2;\n      switch (node.type) {\n        case NodeType.CData:\n          this.cdata(node.value);\n          break;\n        case NodeType.Comment:\n          this.comment(node.value);\n          break;\n        case NodeType.Element:\n          attributes = {};\n          ref1 = node.attribs;\n          for (attName in ref1) {\n            if (!hasProp.call(ref1, attName)) continue;\n            att = ref1[attName];\n            attributes[attName] = att.value;\n          }\n          this.node(node.name, attributes);\n          break;\n        case NodeType.Dummy:\n          this.dummy();\n          break;\n        case NodeType.Raw:\n          this.raw(node.value);\n          break;\n        case NodeType.Text:\n          this.text(node.value);\n          break;\n        case NodeType.ProcessingInstruction:\n          this.instruction(node.target, node.value);\n          break;\n        default:\n          throw new Error(\"This XML node type is not supported in a JS object: \" + node.constructor.name);\n      }\n      ref2 = node.children;\n      for (i = 0, len = ref2.length; i < len; i++) {\n        child = ref2[i];\n        this.createChildNode(child);\n        if (child.type === NodeType.Element) {\n          this.up();\n        }\n      }\n      return this;\n    };\n\n    XMLDocumentCB.prototype.dummy = function() {\n      return this;\n    };\n\n    XMLDocumentCB.prototype.node = function(name, attributes, text) {\n      var ref1;\n      if (name == null) {\n        throw new Error(\"Missing node name.\");\n      }\n      if (this.root && this.currentLevel === -1) {\n        throw new Error(\"Document can only have one root node. \" + this.debugInfo(name));\n      }\n      this.openCurrent();\n      name = getValue(name);\n      if (attributes == null) {\n        attributes = {};\n      }\n      attributes = getValue(attributes);\n      if (!isObject(attributes)) {\n        ref1 = [attributes, text], text = ref1[0], attributes = ref1[1];\n      }\n      this.currentNode = new XMLElement(this, name, attributes);\n      this.currentNode.children = false;\n      this.currentLevel++;\n      this.openTags[this.currentLevel] = this.currentNode;\n      if (text != null) {\n        this.text(text);\n      }\n      return this;\n    };\n\n    XMLDocumentCB.prototype.element = function(name, attributes, text) {\n      var child, i, len, oldValidationFlag, ref1, root;\n      if (this.currentNode && this.currentNode.type === NodeType.DocType) {\n        this.dtdElement.apply(this, arguments);\n      } else {\n        if (Array.isArray(name) || isObject(name) || isFunction(name)) {\n          oldValidationFlag = this.options.noValidation;\n          this.options.noValidation = true;\n          root = new XMLDocument(this.options).element('TEMP_ROOT');\n          root.element(name);\n          this.options.noValidation = oldValidationFlag;\n          ref1 = root.children;\n          for (i = 0, len = ref1.length; i < len; i++) {\n            child = ref1[i];\n            this.createChildNode(child);\n            if (child.type === NodeType.Element) {\n              this.up();\n            }\n          }\n        } else {\n          this.node(name, attributes, text);\n        }\n      }\n      return this;\n    };\n\n    XMLDocumentCB.prototype.attribute = function(name, value) {\n      var attName, attValue;\n      if (!this.currentNode || this.currentNode.children) {\n        throw new Error(\"att() can only be used immediately after an ele() call in callback mode. \" + this.debugInfo(name));\n      }\n      if (name != null) {\n        name = getValue(name);\n      }\n      if (isObject(name)) {\n        for (attName in name) {\n          if (!hasProp.call(name, attName)) continue;\n          attValue = name[attName];\n          this.attribute(attName, attValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        if (this.options.keepNullAttributes && (value == null)) {\n          this.currentNode.attribs[name] = new XMLAttribute(this, name, \"\");\n        } else if (value != null) {\n          this.currentNode.attribs[name] = new XMLAttribute(this, name, value);\n        }\n      }\n      return this;\n    };\n\n    XMLDocumentCB.prototype.text = function(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLText(this, value);\n      this.onData(this.writer.text(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.cdata = function(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLCData(this, value);\n      this.onData(this.writer.cdata(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.comment = function(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLComment(this, value);\n      this.onData(this.writer.comment(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.raw = function(value) {\n      var node;\n      this.openCurrent();\n      node = new XMLRaw(this, value);\n      this.onData(this.writer.raw(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.instruction = function(target, value) {\n      var i, insTarget, insValue, len, node;\n      this.openCurrent();\n      if (target != null) {\n        target = getValue(target);\n      }\n      if (value != null) {\n        value = getValue(value);\n      }\n      if (Array.isArray(target)) {\n        for (i = 0, len = target.length; i < len; i++) {\n          insTarget = target[i];\n          this.instruction(insTarget);\n        }\n      } else if (isObject(target)) {\n        for (insTarget in target) {\n          if (!hasProp.call(target, insTarget)) continue;\n          insValue = target[insTarget];\n          this.instruction(insTarget, insValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        node = new XMLProcessingInstruction(this, target, value);\n        this.onData(this.writer.processingInstruction(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      }\n      return this;\n    };\n\n    XMLDocumentCB.prototype.declaration = function(version, encoding, standalone) {\n      var node;\n      this.openCurrent();\n      if (this.documentStarted) {\n        throw new Error(\"declaration() must be the first node.\");\n      }\n      node = new XMLDeclaration(this, version, encoding, standalone);\n      this.onData(this.writer.declaration(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.doctype = function(root, pubID, sysID) {\n      this.openCurrent();\n      if (root == null) {\n        throw new Error(\"Missing root node name.\");\n      }\n      if (this.root) {\n        throw new Error(\"dtd() must come before the root node.\");\n      }\n      this.currentNode = new XMLDocType(this, pubID, sysID);\n      this.currentNode.rootNodeName = root;\n      this.currentNode.children = false;\n      this.currentLevel++;\n      this.openTags[this.currentLevel] = this.currentNode;\n      return this;\n    };\n\n    XMLDocumentCB.prototype.dtdElement = function(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDElement(this, name, value);\n      this.onData(this.writer.dtdElement(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.attList = function(elementName, attributeName, attributeType, defaultValueType, defaultValue) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDAttList(this, elementName, attributeName, attributeType, defaultValueType, defaultValue);\n      this.onData(this.writer.dtdAttList(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.entity = function(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDEntity(this, false, name, value);\n      this.onData(this.writer.dtdEntity(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.pEntity = function(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDEntity(this, true, name, value);\n      this.onData(this.writer.dtdEntity(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.notation = function(name, value) {\n      var node;\n      this.openCurrent();\n      node = new XMLDTDNotation(this, name, value);\n      this.onData(this.writer.dtdNotation(node, this.writerOptions, this.currentLevel + 1), this.currentLevel + 1);\n      return this;\n    };\n\n    XMLDocumentCB.prototype.up = function() {\n      if (this.currentLevel < 0) {\n        throw new Error(\"The document node has no parent.\");\n      }\n      if (this.currentNode) {\n        if (this.currentNode.children) {\n          this.closeNode(this.currentNode);\n        } else {\n          this.openNode(this.currentNode);\n        }\n        this.currentNode = null;\n      } else {\n        this.closeNode(this.openTags[this.currentLevel]);\n      }\n      delete this.openTags[this.currentLevel];\n      this.currentLevel--;\n      return this;\n    };\n\n    XMLDocumentCB.prototype.end = function() {\n      while (this.currentLevel >= 0) {\n        this.up();\n      }\n      return this.onEnd();\n    };\n\n    XMLDocumentCB.prototype.openCurrent = function() {\n      if (this.currentNode) {\n        this.currentNode.children = true;\n        return this.openNode(this.currentNode);\n      }\n    };\n\n    XMLDocumentCB.prototype.openNode = function(node) {\n      var att, chunk, name, ref1;\n      if (!node.isOpen) {\n        if (!this.root && this.currentLevel === 0 && node.type === NodeType.Element) {\n          this.root = node;\n        }\n        chunk = '';\n        if (node.type === NodeType.Element) {\n          this.writerOptions.state = WriterState.OpenTag;\n          chunk = this.writer.indent(node, this.writerOptions, this.currentLevel) + '<' + node.name;\n          ref1 = node.attribs;\n          for (name in ref1) {\n            if (!hasProp.call(ref1, name)) continue;\n            att = ref1[name];\n            chunk += this.writer.attribute(att, this.writerOptions, this.currentLevel);\n          }\n          chunk += (node.children ? '>' : '/>') + this.writer.endline(node, this.writerOptions, this.currentLevel);\n          this.writerOptions.state = WriterState.InsideTag;\n        } else {\n          this.writerOptions.state = WriterState.OpenTag;\n          chunk = this.writer.indent(node, this.writerOptions, this.currentLevel) + '<!DOCTYPE ' + node.rootNodeName;\n          if (node.pubID && node.sysID) {\n            chunk += ' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"';\n          } else if (node.sysID) {\n            chunk += ' SYSTEM \"' + node.sysID + '\"';\n          }\n          if (node.children) {\n            chunk += ' [';\n            this.writerOptions.state = WriterState.InsideTag;\n          } else {\n            this.writerOptions.state = WriterState.CloseTag;\n            chunk += '>';\n          }\n          chunk += this.writer.endline(node, this.writerOptions, this.currentLevel);\n        }\n        this.onData(chunk, this.currentLevel);\n        return node.isOpen = true;\n      }\n    };\n\n    XMLDocumentCB.prototype.closeNode = function(node) {\n      var chunk;\n      if (!node.isClosed) {\n        chunk = '';\n        this.writerOptions.state = WriterState.CloseTag;\n        if (node.type === NodeType.Element) {\n          chunk = this.writer.indent(node, this.writerOptions, this.currentLevel) + '</' + node.name + '>' + this.writer.endline(node, this.writerOptions, this.currentLevel);\n        } else {\n          chunk = this.writer.indent(node, this.writerOptions, this.currentLevel) + ']>' + this.writer.endline(node, this.writerOptions, this.currentLevel);\n        }\n        this.writerOptions.state = WriterState.None;\n        this.onData(chunk, this.currentLevel);\n        return node.isClosed = true;\n      }\n    };\n\n    XMLDocumentCB.prototype.onData = function(chunk, level) {\n      this.documentStarted = true;\n      return this.onDataCallback(chunk, level + 1);\n    };\n\n    XMLDocumentCB.prototype.onEnd = function() {\n      this.documentCompleted = true;\n      return this.onEndCallback();\n    };\n\n    XMLDocumentCB.prototype.debugInfo = function(name) {\n      if (name == null) {\n        return \"\";\n      } else {\n        return \"node: <\" + name + \">\";\n      }\n    };\n\n    XMLDocumentCB.prototype.ele = function() {\n      return this.element.apply(this, arguments);\n    };\n\n    XMLDocumentCB.prototype.nod = function(name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n\n    XMLDocumentCB.prototype.txt = function(value) {\n      return this.text(value);\n    };\n\n    XMLDocumentCB.prototype.dat = function(value) {\n      return this.cdata(value);\n    };\n\n    XMLDocumentCB.prototype.com = function(value) {\n      return this.comment(value);\n    };\n\n    XMLDocumentCB.prototype.ins = function(target, value) {\n      return this.instruction(target, value);\n    };\n\n    XMLDocumentCB.prototype.dec = function(version, encoding, standalone) {\n      return this.declaration(version, encoding, standalone);\n    };\n\n    XMLDocumentCB.prototype.dtd = function(root, pubID, sysID) {\n      return this.doctype(root, pubID, sysID);\n    };\n\n    XMLDocumentCB.prototype.e = function(name, attributes, text) {\n      return this.element(name, attributes, text);\n    };\n\n    XMLDocumentCB.prototype.n = function(name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n\n    XMLDocumentCB.prototype.t = function(value) {\n      return this.text(value);\n    };\n\n    XMLDocumentCB.prototype.d = function(value) {\n      return this.cdata(value);\n    };\n\n    XMLDocumentCB.prototype.c = function(value) {\n      return this.comment(value);\n    };\n\n    XMLDocumentCB.prototype.r = function(value) {\n      return this.raw(value);\n    };\n\n    XMLDocumentCB.prototype.i = function(target, value) {\n      return this.instruction(target, value);\n    };\n\n    XMLDocumentCB.prototype.att = function() {\n      if (this.currentNode && this.currentNode.type === NodeType.DocType) {\n        return this.attList.apply(this, arguments);\n      } else {\n        return this.attribute.apply(this, arguments);\n      }\n    };\n\n    XMLDocumentCB.prototype.a = function() {\n      if (this.currentNode && this.currentNode.type === NodeType.DocType) {\n        return this.attList.apply(this, arguments);\n      } else {\n        return this.attribute.apply(this, arguments);\n      }\n    };\n\n    XMLDocumentCB.prototype.ent = function(name, value) {\n      return this.entity(name, value);\n    };\n\n    XMLDocumentCB.prototype.pent = function(name, value) {\n      return this.pEntity(name, value);\n    };\n\n    XMLDocumentCB.prototype.not = function(name, value) {\n      return this.notation(name, value);\n    };\n\n    return XMLDocumentCB;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLDocumentCB.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLDummy.js":
/*!*************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLDummy.js ***!
  \*************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLDummy, XMLNode,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  module.exports = XMLDummy = (function(superClass) {\n    extend(XMLDummy, superClass);\n\n    function XMLDummy(parent) {\n      XMLDummy.__super__.constructor.call(this, parent);\n      this.type = NodeType.Dummy;\n    }\n\n    XMLDummy.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLDummy.prototype.toString = function(options) {\n      return '';\n    };\n\n    return XMLDummy;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMveG1sYnVpbGRlci9saWIvWE1MRHVtbXkuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0EsdUNBQXVDLDBCQUEwQiwyREFBMkQsa0JBQWtCLDRCQUE0QixtQ0FBbUMsOEJBQThCLG9DQUFvQyxlQUFlO0FBQzlSLGdCQUFnQjs7QUFFaEIsWUFBWSxtQkFBTyxDQUFDLGlFQUFXOztBQUUvQixhQUFhLG1CQUFPLENBQUMsbUVBQVk7O0FBRWpDO0FBQ0E7O0FBRUE7QUFDQTtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTs7QUFFQTs7QUFFQSxHQUFHOztBQUVILENBQUMiLCJzb3VyY2VzIjpbIi9Vc2Vycy9zYW50aG9zaHBhbGFuaXNhbXkvcHJvamVjdHMvQWdlbnREZXZlbG9wbWVudC90d2l0dGVyYm90L3R3aXR0ZXItYm90LWRhc2hib2FyZC9ub2RlX21vZHVsZXMveG1sYnVpbGRlci9saWIvWE1MRHVtbXkuanMiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gR2VuZXJhdGVkIGJ5IENvZmZlZVNjcmlwdCAxLjEyLjdcbihmdW5jdGlvbigpIHtcbiAgdmFyIE5vZGVUeXBlLCBYTUxEdW1teSwgWE1MTm9kZSxcbiAgICBleHRlbmQgPSBmdW5jdGlvbihjaGlsZCwgcGFyZW50KSB7IGZvciAodmFyIGtleSBpbiBwYXJlbnQpIHsgaWYgKGhhc1Byb3AuY2FsbChwYXJlbnQsIGtleSkpIGNoaWxkW2tleV0gPSBwYXJlbnRba2V5XTsgfSBmdW5jdGlvbiBjdG9yKCkgeyB0aGlzLmNvbnN0cnVjdG9yID0gY2hpbGQ7IH0gY3Rvci5wcm90b3R5cGUgPSBwYXJlbnQucHJvdG90eXBlOyBjaGlsZC5wcm90b3R5cGUgPSBuZXcgY3RvcigpOyBjaGlsZC5fX3N1cGVyX18gPSBwYXJlbnQucHJvdG90eXBlOyByZXR1cm4gY2hpbGQ7IH0sXG4gICAgaGFzUHJvcCA9IHt9Lmhhc093blByb3BlcnR5O1xuXG4gIFhNTE5vZGUgPSByZXF1aXJlKCcuL1hNTE5vZGUnKTtcblxuICBOb2RlVHlwZSA9IHJlcXVpcmUoJy4vTm9kZVR5cGUnKTtcblxuICBtb2R1bGUuZXhwb3J0cyA9IFhNTER1bW15ID0gKGZ1bmN0aW9uKHN1cGVyQ2xhc3MpIHtcbiAgICBleHRlbmQoWE1MRHVtbXksIHN1cGVyQ2xhc3MpO1xuXG4gICAgZnVuY3Rpb24gWE1MRHVtbXkocGFyZW50KSB7XG4gICAgICBYTUxEdW1teS5fX3N1cGVyX18uY29uc3RydWN0b3IuY2FsbCh0aGlzLCBwYXJlbnQpO1xuICAgICAgdGhpcy50eXBlID0gTm9kZVR5cGUuRHVtbXk7XG4gICAgfVxuXG4gICAgWE1MRHVtbXkucHJvdG90eXBlLmNsb25lID0gZnVuY3Rpb24oKSB7XG4gICAgICByZXR1cm4gT2JqZWN0LmNyZWF0ZSh0aGlzKTtcbiAgICB9O1xuXG4gICAgWE1MRHVtbXkucHJvdG90eXBlLnRvU3RyaW5nID0gZnVuY3Rpb24ob3B0aW9ucykge1xuICAgICAgcmV0dXJuICcnO1xuICAgIH07XG5cbiAgICByZXR1cm4gWE1MRHVtbXk7XG5cbiAgfSkoWE1MTm9kZSk7XG5cbn0pLmNhbGwodGhpcyk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLDummy.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLElement.js":
/*!***************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLElement.js ***!
  \***************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLAttribute, XMLElement, XMLNamedNodeMap, XMLNode, getValue, isFunction, isObject, ref,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  ref = __webpack_require__(/*! ./Utility */ \"(rsc)/./node_modules/xmlbuilder/lib/Utility.js\"), isObject = ref.isObject, isFunction = ref.isFunction, getValue = ref.getValue;\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLAttribute = __webpack_require__(/*! ./XMLAttribute */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLAttribute.js\");\n\n  XMLNamedNodeMap = __webpack_require__(/*! ./XMLNamedNodeMap */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNamedNodeMap.js\");\n\n  module.exports = XMLElement = (function(superClass) {\n    extend(XMLElement, superClass);\n\n    function XMLElement(parent, name, attributes) {\n      var child, j, len, ref1;\n      XMLElement.__super__.constructor.call(this, parent);\n      if (name == null) {\n        throw new Error(\"Missing element name. \" + this.debugInfo());\n      }\n      this.name = this.stringify.name(name);\n      this.type = NodeType.Element;\n      this.attribs = {};\n      this.schemaTypeInfo = null;\n      if (attributes != null) {\n        this.attribute(attributes);\n      }\n      if (parent.type === NodeType.Document) {\n        this.isRoot = true;\n        this.documentObject = parent;\n        parent.rootObject = this;\n        if (parent.children) {\n          ref1 = parent.children;\n          for (j = 0, len = ref1.length; j < len; j++) {\n            child = ref1[j];\n            if (child.type === NodeType.DocType) {\n              child.name = this.name;\n              break;\n            }\n          }\n        }\n      }\n    }\n\n    Object.defineProperty(XMLElement.prototype, 'tagName', {\n      get: function() {\n        return this.name;\n      }\n    });\n\n    Object.defineProperty(XMLElement.prototype, 'namespaceURI', {\n      get: function() {\n        return '';\n      }\n    });\n\n    Object.defineProperty(XMLElement.prototype, 'prefix', {\n      get: function() {\n        return '';\n      }\n    });\n\n    Object.defineProperty(XMLElement.prototype, 'localName', {\n      get: function() {\n        return this.name;\n      }\n    });\n\n    Object.defineProperty(XMLElement.prototype, 'id', {\n      get: function() {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n    });\n\n    Object.defineProperty(XMLElement.prototype, 'className', {\n      get: function() {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n    });\n\n    Object.defineProperty(XMLElement.prototype, 'classList', {\n      get: function() {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n    });\n\n    Object.defineProperty(XMLElement.prototype, 'attributes', {\n      get: function() {\n        if (!this.attributeMap || !this.attributeMap.nodes) {\n          this.attributeMap = new XMLNamedNodeMap(this.attribs);\n        }\n        return this.attributeMap;\n      }\n    });\n\n    XMLElement.prototype.clone = function() {\n      var att, attName, clonedSelf, ref1;\n      clonedSelf = Object.create(this);\n      if (clonedSelf.isRoot) {\n        clonedSelf.documentObject = null;\n      }\n      clonedSelf.attribs = {};\n      ref1 = this.attribs;\n      for (attName in ref1) {\n        if (!hasProp.call(ref1, attName)) continue;\n        att = ref1[attName];\n        clonedSelf.attribs[attName] = att.clone();\n      }\n      clonedSelf.children = [];\n      this.children.forEach(function(child) {\n        var clonedChild;\n        clonedChild = child.clone();\n        clonedChild.parent = clonedSelf;\n        return clonedSelf.children.push(clonedChild);\n      });\n      return clonedSelf;\n    };\n\n    XMLElement.prototype.attribute = function(name, value) {\n      var attName, attValue;\n      if (name != null) {\n        name = getValue(name);\n      }\n      if (isObject(name)) {\n        for (attName in name) {\n          if (!hasProp.call(name, attName)) continue;\n          attValue = name[attName];\n          this.attribute(attName, attValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        if (this.options.keepNullAttributes && (value == null)) {\n          this.attribs[name] = new XMLAttribute(this, name, \"\");\n        } else if (value != null) {\n          this.attribs[name] = new XMLAttribute(this, name, value);\n        }\n      }\n      return this;\n    };\n\n    XMLElement.prototype.removeAttribute = function(name) {\n      var attName, j, len;\n      if (name == null) {\n        throw new Error(\"Missing attribute name. \" + this.debugInfo());\n      }\n      name = getValue(name);\n      if (Array.isArray(name)) {\n        for (j = 0, len = name.length; j < len; j++) {\n          attName = name[j];\n          delete this.attribs[attName];\n        }\n      } else {\n        delete this.attribs[name];\n      }\n      return this;\n    };\n\n    XMLElement.prototype.toString = function(options) {\n      return this.options.writer.element(this, this.options.writer.filterOptions(options));\n    };\n\n    XMLElement.prototype.att = function(name, value) {\n      return this.attribute(name, value);\n    };\n\n    XMLElement.prototype.a = function(name, value) {\n      return this.attribute(name, value);\n    };\n\n    XMLElement.prototype.getAttribute = function(name) {\n      if (this.attribs.hasOwnProperty(name)) {\n        return this.attribs[name].value;\n      } else {\n        return null;\n      }\n    };\n\n    XMLElement.prototype.setAttribute = function(name, value) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.getAttributeNode = function(name) {\n      if (this.attribs.hasOwnProperty(name)) {\n        return this.attribs[name];\n      } else {\n        return null;\n      }\n    };\n\n    XMLElement.prototype.setAttributeNode = function(newAttr) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.removeAttributeNode = function(oldAttr) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.getElementsByTagName = function(name) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.getAttributeNS = function(namespaceURI, localName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.setAttributeNS = function(namespaceURI, qualifiedName, value) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.removeAttributeNS = function(namespaceURI, localName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.getAttributeNodeNS = function(namespaceURI, localName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.setAttributeNodeNS = function(newAttr) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.getElementsByTagNameNS = function(namespaceURI, localName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.hasAttribute = function(name) {\n      return this.attribs.hasOwnProperty(name);\n    };\n\n    XMLElement.prototype.hasAttributeNS = function(namespaceURI, localName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.setIdAttribute = function(name, isId) {\n      if (this.attribs.hasOwnProperty(name)) {\n        return this.attribs[name].isId;\n      } else {\n        return isId;\n      }\n    };\n\n    XMLElement.prototype.setIdAttributeNS = function(namespaceURI, localName, isId) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.setIdAttributeNode = function(idAttr, isId) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.getElementsByTagName = function(tagname) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.getElementsByTagNameNS = function(namespaceURI, localName) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.getElementsByClassName = function(classNames) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLElement.prototype.isEqualNode = function(node) {\n      var i, j, ref1;\n      if (!XMLElement.__super__.isEqualNode.apply(this, arguments).isEqualNode(node)) {\n        return false;\n      }\n      if (node.namespaceURI !== this.namespaceURI) {\n        return false;\n      }\n      if (node.prefix !== this.prefix) {\n        return false;\n      }\n      if (node.localName !== this.localName) {\n        return false;\n      }\n      if (node.attribs.length !== this.attribs.length) {\n        return false;\n      }\n      for (i = j = 0, ref1 = this.attribs.length - 1; 0 <= ref1 ? j <= ref1 : j >= ref1; i = 0 <= ref1 ? ++j : --j) {\n        if (!this.attribs[i].isEqualNode(node.attribs[i])) {\n          return false;\n        }\n      }\n      return true;\n    };\n\n    return XMLElement;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLElement.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLNamedNodeMap.js":
/*!********************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLNamedNodeMap.js ***!
  \********************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLNamedNodeMap;\n\n  module.exports = XMLNamedNodeMap = (function() {\n    function XMLNamedNodeMap(nodes) {\n      this.nodes = nodes;\n    }\n\n    Object.defineProperty(XMLNamedNodeMap.prototype, 'length', {\n      get: function() {\n        return Object.keys(this.nodes).length || 0;\n      }\n    });\n\n    XMLNamedNodeMap.prototype.clone = function() {\n      return this.nodes = null;\n    };\n\n    XMLNamedNodeMap.prototype.getNamedItem = function(name) {\n      return this.nodes[name];\n    };\n\n    XMLNamedNodeMap.prototype.setNamedItem = function(node) {\n      var oldNode;\n      oldNode = this.nodes[node.nodeName];\n      this.nodes[node.nodeName] = node;\n      return oldNode || null;\n    };\n\n    XMLNamedNodeMap.prototype.removeNamedItem = function(name) {\n      var oldNode;\n      oldNode = this.nodes[name];\n      delete this.nodes[name];\n      return oldNode || null;\n    };\n\n    XMLNamedNodeMap.prototype.item = function(index) {\n      return this.nodes[Object.keys(this.nodes)[index]] || null;\n    };\n\n    XMLNamedNodeMap.prototype.getNamedItemNS = function(namespaceURI, localName) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n\n    XMLNamedNodeMap.prototype.setNamedItemNS = function(node) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n\n    XMLNamedNodeMap.prototype.removeNamedItemNS = function(namespaceURI, localName) {\n      throw new Error(\"This DOM method is not implemented.\");\n    };\n\n    return XMLNamedNodeMap;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLNamedNodeMap.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLNode.js":
/*!************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLNode.js ***!
  \************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var DocumentPosition, NodeType, XMLCData, XMLComment, XMLDeclaration, XMLDocType, XMLDummy, XMLElement, XMLNamedNodeMap, XMLNode, XMLNodeList, XMLProcessingInstruction, XMLRaw, XMLText, getValue, isEmpty, isFunction, isObject, ref1,\n    hasProp = {}.hasOwnProperty;\n\n  ref1 = __webpack_require__(/*! ./Utility */ \"(rsc)/./node_modules/xmlbuilder/lib/Utility.js\"), isObject = ref1.isObject, isFunction = ref1.isFunction, isEmpty = ref1.isEmpty, getValue = ref1.getValue;\n\n  XMLElement = null;\n\n  XMLCData = null;\n\n  XMLComment = null;\n\n  XMLDeclaration = null;\n\n  XMLDocType = null;\n\n  XMLRaw = null;\n\n  XMLText = null;\n\n  XMLProcessingInstruction = null;\n\n  XMLDummy = null;\n\n  NodeType = null;\n\n  XMLNodeList = null;\n\n  XMLNamedNodeMap = null;\n\n  DocumentPosition = null;\n\n  module.exports = XMLNode = (function() {\n    function XMLNode(parent1) {\n      this.parent = parent1;\n      if (this.parent) {\n        this.options = this.parent.options;\n        this.stringify = this.parent.stringify;\n      }\n      this.value = null;\n      this.children = [];\n      this.baseURI = null;\n      if (!XMLElement) {\n        XMLElement = __webpack_require__(/*! ./XMLElement */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLElement.js\");\n        XMLCData = __webpack_require__(/*! ./XMLCData */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLCData.js\");\n        XMLComment = __webpack_require__(/*! ./XMLComment */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLComment.js\");\n        XMLDeclaration = __webpack_require__(/*! ./XMLDeclaration */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDeclaration.js\");\n        XMLDocType = __webpack_require__(/*! ./XMLDocType */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDocType.js\");\n        XMLRaw = __webpack_require__(/*! ./XMLRaw */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLRaw.js\");\n        XMLText = __webpack_require__(/*! ./XMLText */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLText.js\");\n        XMLProcessingInstruction = __webpack_require__(/*! ./XMLProcessingInstruction */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLProcessingInstruction.js\");\n        XMLDummy = __webpack_require__(/*! ./XMLDummy */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDummy.js\");\n        NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n        XMLNodeList = __webpack_require__(/*! ./XMLNodeList */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNodeList.js\");\n        XMLNamedNodeMap = __webpack_require__(/*! ./XMLNamedNodeMap */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNamedNodeMap.js\");\n        DocumentPosition = __webpack_require__(/*! ./DocumentPosition */ \"(rsc)/./node_modules/xmlbuilder/lib/DocumentPosition.js\");\n      }\n    }\n\n    Object.defineProperty(XMLNode.prototype, 'nodeName', {\n      get: function() {\n        return this.name;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'nodeType', {\n      get: function() {\n        return this.type;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'nodeValue', {\n      get: function() {\n        return this.value;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'parentNode', {\n      get: function() {\n        return this.parent;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'childNodes', {\n      get: function() {\n        if (!this.childNodeList || !this.childNodeList.nodes) {\n          this.childNodeList = new XMLNodeList(this.children);\n        }\n        return this.childNodeList;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'firstChild', {\n      get: function() {\n        return this.children[0] || null;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'lastChild', {\n      get: function() {\n        return this.children[this.children.length - 1] || null;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'previousSibling', {\n      get: function() {\n        var i;\n        i = this.parent.children.indexOf(this);\n        return this.parent.children[i - 1] || null;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'nextSibling', {\n      get: function() {\n        var i;\n        i = this.parent.children.indexOf(this);\n        return this.parent.children[i + 1] || null;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'ownerDocument', {\n      get: function() {\n        return this.document() || null;\n      }\n    });\n\n    Object.defineProperty(XMLNode.prototype, 'textContent', {\n      get: function() {\n        var child, j, len, ref2, str;\n        if (this.nodeType === NodeType.Element || this.nodeType === NodeType.DocumentFragment) {\n          str = '';\n          ref2 = this.children;\n          for (j = 0, len = ref2.length; j < len; j++) {\n            child = ref2[j];\n            if (child.textContent) {\n              str += child.textContent;\n            }\n          }\n          return str;\n        } else {\n          return null;\n        }\n      },\n      set: function(value) {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n    });\n\n    XMLNode.prototype.setParent = function(parent) {\n      var child, j, len, ref2, results;\n      this.parent = parent;\n      if (parent) {\n        this.options = parent.options;\n        this.stringify = parent.stringify;\n      }\n      ref2 = this.children;\n      results = [];\n      for (j = 0, len = ref2.length; j < len; j++) {\n        child = ref2[j];\n        results.push(child.setParent(this));\n      }\n      return results;\n    };\n\n    XMLNode.prototype.element = function(name, attributes, text) {\n      var childNode, item, j, k, key, lastChild, len, len1, ref2, ref3, val;\n      lastChild = null;\n      if (attributes === null && (text == null)) {\n        ref2 = [{}, null], attributes = ref2[0], text = ref2[1];\n      }\n      if (attributes == null) {\n        attributes = {};\n      }\n      attributes = getValue(attributes);\n      if (!isObject(attributes)) {\n        ref3 = [attributes, text], text = ref3[0], attributes = ref3[1];\n      }\n      if (name != null) {\n        name = getValue(name);\n      }\n      if (Array.isArray(name)) {\n        for (j = 0, len = name.length; j < len; j++) {\n          item = name[j];\n          lastChild = this.element(item);\n        }\n      } else if (isFunction(name)) {\n        lastChild = this.element(name.apply());\n      } else if (isObject(name)) {\n        for (key in name) {\n          if (!hasProp.call(name, key)) continue;\n          val = name[key];\n          if (isFunction(val)) {\n            val = val.apply();\n          }\n          if (!this.options.ignoreDecorators && this.stringify.convertAttKey && key.indexOf(this.stringify.convertAttKey) === 0) {\n            lastChild = this.attribute(key.substr(this.stringify.convertAttKey.length), val);\n          } else if (!this.options.separateArrayItems && Array.isArray(val) && isEmpty(val)) {\n            lastChild = this.dummy();\n          } else if (isObject(val) && isEmpty(val)) {\n            lastChild = this.element(key);\n          } else if (!this.options.keepNullNodes && (val == null)) {\n            lastChild = this.dummy();\n          } else if (!this.options.separateArrayItems && Array.isArray(val)) {\n            for (k = 0, len1 = val.length; k < len1; k++) {\n              item = val[k];\n              childNode = {};\n              childNode[key] = item;\n              lastChild = this.element(childNode);\n            }\n          } else if (isObject(val)) {\n            if (!this.options.ignoreDecorators && this.stringify.convertTextKey && key.indexOf(this.stringify.convertTextKey) === 0) {\n              lastChild = this.element(val);\n            } else {\n              lastChild = this.element(key);\n              lastChild.element(val);\n            }\n          } else {\n            lastChild = this.element(key, val);\n          }\n        }\n      } else if (!this.options.keepNullNodes && text === null) {\n        lastChild = this.dummy();\n      } else {\n        if (!this.options.ignoreDecorators && this.stringify.convertTextKey && name.indexOf(this.stringify.convertTextKey) === 0) {\n          lastChild = this.text(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertCDataKey && name.indexOf(this.stringify.convertCDataKey) === 0) {\n          lastChild = this.cdata(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertCommentKey && name.indexOf(this.stringify.convertCommentKey) === 0) {\n          lastChild = this.comment(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertRawKey && name.indexOf(this.stringify.convertRawKey) === 0) {\n          lastChild = this.raw(text);\n        } else if (!this.options.ignoreDecorators && this.stringify.convertPIKey && name.indexOf(this.stringify.convertPIKey) === 0) {\n          lastChild = this.instruction(name.substr(this.stringify.convertPIKey.length), text);\n        } else {\n          lastChild = this.node(name, attributes, text);\n        }\n      }\n      if (lastChild == null) {\n        throw new Error(\"Could not create any elements with: \" + name + \". \" + this.debugInfo());\n      }\n      return lastChild;\n    };\n\n    XMLNode.prototype.insertBefore = function(name, attributes, text) {\n      var child, i, newChild, refChild, removed;\n      if (name != null ? name.type : void 0) {\n        newChild = name;\n        refChild = attributes;\n        newChild.setParent(this);\n        if (refChild) {\n          i = children.indexOf(refChild);\n          removed = children.splice(i);\n          children.push(newChild);\n          Array.prototype.push.apply(children, removed);\n        } else {\n          children.push(newChild);\n        }\n        return newChild;\n      } else {\n        if (this.isRoot) {\n          throw new Error(\"Cannot insert elements at root level. \" + this.debugInfo(name));\n        }\n        i = this.parent.children.indexOf(this);\n        removed = this.parent.children.splice(i);\n        child = this.parent.element(name, attributes, text);\n        Array.prototype.push.apply(this.parent.children, removed);\n        return child;\n      }\n    };\n\n    XMLNode.prototype.insertAfter = function(name, attributes, text) {\n      var child, i, removed;\n      if (this.isRoot) {\n        throw new Error(\"Cannot insert elements at root level. \" + this.debugInfo(name));\n      }\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i + 1);\n      child = this.parent.element(name, attributes, text);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return child;\n    };\n\n    XMLNode.prototype.remove = function() {\n      var i, ref2;\n      if (this.isRoot) {\n        throw new Error(\"Cannot remove the root element. \" + this.debugInfo());\n      }\n      i = this.parent.children.indexOf(this);\n      [].splice.apply(this.parent.children, [i, i - i + 1].concat(ref2 = [])), ref2;\n      return this.parent;\n    };\n\n    XMLNode.prototype.node = function(name, attributes, text) {\n      var child, ref2;\n      if (name != null) {\n        name = getValue(name);\n      }\n      attributes || (attributes = {});\n      attributes = getValue(attributes);\n      if (!isObject(attributes)) {\n        ref2 = [attributes, text], text = ref2[0], attributes = ref2[1];\n      }\n      child = new XMLElement(this, name, attributes);\n      if (text != null) {\n        child.text(text);\n      }\n      this.children.push(child);\n      return child;\n    };\n\n    XMLNode.prototype.text = function(value) {\n      var child;\n      if (isObject(value)) {\n        this.element(value);\n      }\n      child = new XMLText(this, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLNode.prototype.cdata = function(value) {\n      var child;\n      child = new XMLCData(this, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLNode.prototype.comment = function(value) {\n      var child;\n      child = new XMLComment(this, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLNode.prototype.commentBefore = function(value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i);\n      child = this.parent.comment(value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n\n    XMLNode.prototype.commentAfter = function(value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i + 1);\n      child = this.parent.comment(value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n\n    XMLNode.prototype.raw = function(value) {\n      var child;\n      child = new XMLRaw(this, value);\n      this.children.push(child);\n      return this;\n    };\n\n    XMLNode.prototype.dummy = function() {\n      var child;\n      child = new XMLDummy(this);\n      return child;\n    };\n\n    XMLNode.prototype.instruction = function(target, value) {\n      var insTarget, insValue, instruction, j, len;\n      if (target != null) {\n        target = getValue(target);\n      }\n      if (value != null) {\n        value = getValue(value);\n      }\n      if (Array.isArray(target)) {\n        for (j = 0, len = target.length; j < len; j++) {\n          insTarget = target[j];\n          this.instruction(insTarget);\n        }\n      } else if (isObject(target)) {\n        for (insTarget in target) {\n          if (!hasProp.call(target, insTarget)) continue;\n          insValue = target[insTarget];\n          this.instruction(insTarget, insValue);\n        }\n      } else {\n        if (isFunction(value)) {\n          value = value.apply();\n        }\n        instruction = new XMLProcessingInstruction(this, target, value);\n        this.children.push(instruction);\n      }\n      return this;\n    };\n\n    XMLNode.prototype.instructionBefore = function(target, value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i);\n      child = this.parent.instruction(target, value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n\n    XMLNode.prototype.instructionAfter = function(target, value) {\n      var child, i, removed;\n      i = this.parent.children.indexOf(this);\n      removed = this.parent.children.splice(i + 1);\n      child = this.parent.instruction(target, value);\n      Array.prototype.push.apply(this.parent.children, removed);\n      return this;\n    };\n\n    XMLNode.prototype.declaration = function(version, encoding, standalone) {\n      var doc, xmldec;\n      doc = this.document();\n      xmldec = new XMLDeclaration(doc, version, encoding, standalone);\n      if (doc.children.length === 0) {\n        doc.children.unshift(xmldec);\n      } else if (doc.children[0].type === NodeType.Declaration) {\n        doc.children[0] = xmldec;\n      } else {\n        doc.children.unshift(xmldec);\n      }\n      return doc.root() || doc;\n    };\n\n    XMLNode.prototype.dtd = function(pubID, sysID) {\n      var child, doc, doctype, i, j, k, len, len1, ref2, ref3;\n      doc = this.document();\n      doctype = new XMLDocType(doc, pubID, sysID);\n      ref2 = doc.children;\n      for (i = j = 0, len = ref2.length; j < len; i = ++j) {\n        child = ref2[i];\n        if (child.type === NodeType.DocType) {\n          doc.children[i] = doctype;\n          return doctype;\n        }\n      }\n      ref3 = doc.children;\n      for (i = k = 0, len1 = ref3.length; k < len1; i = ++k) {\n        child = ref3[i];\n        if (child.isRoot) {\n          doc.children.splice(i, 0, doctype);\n          return doctype;\n        }\n      }\n      doc.children.push(doctype);\n      return doctype;\n    };\n\n    XMLNode.prototype.up = function() {\n      if (this.isRoot) {\n        throw new Error(\"The root node has no parent. Use doc() if you need to get the document object.\");\n      }\n      return this.parent;\n    };\n\n    XMLNode.prototype.root = function() {\n      var node;\n      node = this;\n      while (node) {\n        if (node.type === NodeType.Document) {\n          return node.rootObject;\n        } else if (node.isRoot) {\n          return node;\n        } else {\n          node = node.parent;\n        }\n      }\n    };\n\n    XMLNode.prototype.document = function() {\n      var node;\n      node = this;\n      while (node) {\n        if (node.type === NodeType.Document) {\n          return node;\n        } else {\n          node = node.parent;\n        }\n      }\n    };\n\n    XMLNode.prototype.end = function(options) {\n      return this.document().end(options);\n    };\n\n    XMLNode.prototype.prev = function() {\n      var i;\n      i = this.parent.children.indexOf(this);\n      if (i < 1) {\n        throw new Error(\"Already at the first node. \" + this.debugInfo());\n      }\n      return this.parent.children[i - 1];\n    };\n\n    XMLNode.prototype.next = function() {\n      var i;\n      i = this.parent.children.indexOf(this);\n      if (i === -1 || i === this.parent.children.length - 1) {\n        throw new Error(\"Already at the last node. \" + this.debugInfo());\n      }\n      return this.parent.children[i + 1];\n    };\n\n    XMLNode.prototype.importDocument = function(doc) {\n      var clonedRoot;\n      clonedRoot = doc.root().clone();\n      clonedRoot.parent = this;\n      clonedRoot.isRoot = false;\n      this.children.push(clonedRoot);\n      return this;\n    };\n\n    XMLNode.prototype.debugInfo = function(name) {\n      var ref2, ref3;\n      name = name || this.name;\n      if ((name == null) && !((ref2 = this.parent) != null ? ref2.name : void 0)) {\n        return \"\";\n      } else if (name == null) {\n        return \"parent: <\" + this.parent.name + \">\";\n      } else if (!((ref3 = this.parent) != null ? ref3.name : void 0)) {\n        return \"node: <\" + name + \">\";\n      } else {\n        return \"node: <\" + name + \">, parent: <\" + this.parent.name + \">\";\n      }\n    };\n\n    XMLNode.prototype.ele = function(name, attributes, text) {\n      return this.element(name, attributes, text);\n    };\n\n    XMLNode.prototype.nod = function(name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n\n    XMLNode.prototype.txt = function(value) {\n      return this.text(value);\n    };\n\n    XMLNode.prototype.dat = function(value) {\n      return this.cdata(value);\n    };\n\n    XMLNode.prototype.com = function(value) {\n      return this.comment(value);\n    };\n\n    XMLNode.prototype.ins = function(target, value) {\n      return this.instruction(target, value);\n    };\n\n    XMLNode.prototype.doc = function() {\n      return this.document();\n    };\n\n    XMLNode.prototype.dec = function(version, encoding, standalone) {\n      return this.declaration(version, encoding, standalone);\n    };\n\n    XMLNode.prototype.e = function(name, attributes, text) {\n      return this.element(name, attributes, text);\n    };\n\n    XMLNode.prototype.n = function(name, attributes, text) {\n      return this.node(name, attributes, text);\n    };\n\n    XMLNode.prototype.t = function(value) {\n      return this.text(value);\n    };\n\n    XMLNode.prototype.d = function(value) {\n      return this.cdata(value);\n    };\n\n    XMLNode.prototype.c = function(value) {\n      return this.comment(value);\n    };\n\n    XMLNode.prototype.r = function(value) {\n      return this.raw(value);\n    };\n\n    XMLNode.prototype.i = function(target, value) {\n      return this.instruction(target, value);\n    };\n\n    XMLNode.prototype.u = function() {\n      return this.up();\n    };\n\n    XMLNode.prototype.importXMLBuilder = function(doc) {\n      return this.importDocument(doc);\n    };\n\n    XMLNode.prototype.replaceChild = function(newChild, oldChild) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.removeChild = function(oldChild) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.appendChild = function(newChild) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.hasChildNodes = function() {\n      return this.children.length !== 0;\n    };\n\n    XMLNode.prototype.cloneNode = function(deep) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.normalize = function() {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.isSupported = function(feature, version) {\n      return true;\n    };\n\n    XMLNode.prototype.hasAttributes = function() {\n      return this.attribs.length !== 0;\n    };\n\n    XMLNode.prototype.compareDocumentPosition = function(other) {\n      var ref, res;\n      ref = this;\n      if (ref === other) {\n        return 0;\n      } else if (this.document() !== other.document()) {\n        res = DocumentPosition.Disconnected | DocumentPosition.ImplementationSpecific;\n        if (Math.random() < 0.5) {\n          res |= DocumentPosition.Preceding;\n        } else {\n          res |= DocumentPosition.Following;\n        }\n        return res;\n      } else if (ref.isAncestor(other)) {\n        return DocumentPosition.Contains | DocumentPosition.Preceding;\n      } else if (ref.isDescendant(other)) {\n        return DocumentPosition.Contains | DocumentPosition.Following;\n      } else if (ref.isPreceding(other)) {\n        return DocumentPosition.Preceding;\n      } else {\n        return DocumentPosition.Following;\n      }\n    };\n\n    XMLNode.prototype.isSameNode = function(other) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.lookupPrefix = function(namespaceURI) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.isDefaultNamespace = function(namespaceURI) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.lookupNamespaceURI = function(prefix) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.isEqualNode = function(node) {\n      var i, j, ref2;\n      if (node.nodeType !== this.nodeType) {\n        return false;\n      }\n      if (node.children.length !== this.children.length) {\n        return false;\n      }\n      for (i = j = 0, ref2 = this.children.length - 1; 0 <= ref2 ? j <= ref2 : j >= ref2; i = 0 <= ref2 ? ++j : --j) {\n        if (!this.children[i].isEqualNode(node.children[i])) {\n          return false;\n        }\n      }\n      return true;\n    };\n\n    XMLNode.prototype.getFeature = function(feature, version) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.setUserData = function(key, data, handler) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.getUserData = function(key) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLNode.prototype.contains = function(other) {\n      if (!other) {\n        return false;\n      }\n      return other === this || this.isDescendant(other);\n    };\n\n    XMLNode.prototype.isDescendant = function(node) {\n      var child, isDescendantChild, j, len, ref2;\n      ref2 = this.children;\n      for (j = 0, len = ref2.length; j < len; j++) {\n        child = ref2[j];\n        if (node === child) {\n          return true;\n        }\n        isDescendantChild = child.isDescendant(node);\n        if (isDescendantChild) {\n          return true;\n        }\n      }\n      return false;\n    };\n\n    XMLNode.prototype.isAncestor = function(node) {\n      return node.isDescendant(this);\n    };\n\n    XMLNode.prototype.isPreceding = function(node) {\n      var nodePos, thisPos;\n      nodePos = this.treePosition(node);\n      thisPos = this.treePosition(this);\n      if (nodePos === -1 || thisPos === -1) {\n        return false;\n      } else {\n        return nodePos < thisPos;\n      }\n    };\n\n    XMLNode.prototype.isFollowing = function(node) {\n      var nodePos, thisPos;\n      nodePos = this.treePosition(node);\n      thisPos = this.treePosition(this);\n      if (nodePos === -1 || thisPos === -1) {\n        return false;\n      } else {\n        return nodePos > thisPos;\n      }\n    };\n\n    XMLNode.prototype.treePosition = function(node) {\n      var found, pos;\n      pos = 0;\n      found = false;\n      this.foreachTreeNode(this.document(), function(childNode) {\n        pos++;\n        if (!found && childNode === node) {\n          return found = true;\n        }\n      });\n      if (found) {\n        return pos;\n      } else {\n        return -1;\n      }\n    };\n\n    XMLNode.prototype.foreachTreeNode = function(node, func) {\n      var child, j, len, ref2, res;\n      node || (node = this.document());\n      ref2 = node.children;\n      for (j = 0, len = ref2.length; j < len; j++) {\n        child = ref2[j];\n        if (res = func(child)) {\n          return res;\n        } else {\n          res = this.foreachTreeNode(child, func);\n          if (res) {\n            return res;\n          }\n        }\n      }\n    };\n\n    return XMLNode;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLNode.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLNodeList.js":
/*!****************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLNodeList.js ***!
  \****************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLNodeList;\n\n  module.exports = XMLNodeList = (function() {\n    function XMLNodeList(nodes) {\n      this.nodes = nodes;\n    }\n\n    Object.defineProperty(XMLNodeList.prototype, 'length', {\n      get: function() {\n        return this.nodes.length || 0;\n      }\n    });\n\n    XMLNodeList.prototype.clone = function() {\n      return this.nodes = null;\n    };\n\n    XMLNodeList.prototype.item = function(index) {\n      return this.nodes[index] || null;\n    };\n\n    return XMLNodeList;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9ub2RlX21vZHVsZXMveG1sYnVpbGRlci9saWIvWE1MTm9kZUxpc3QuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBOztBQUVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSzs7QUFFTDtBQUNBO0FBQ0E7O0FBRUE7QUFDQTtBQUNBOztBQUVBOztBQUVBLEdBQUc7O0FBRUgsQ0FBQyIsInNvdXJjZXMiOlsiL1VzZXJzL3NhbnRob3NocGFsYW5pc2FteS9wcm9qZWN0cy9BZ2VudERldmVsb3BtZW50L3R3aXR0ZXJib3QvdHdpdHRlci1ib3QtZGFzaGJvYXJkL25vZGVfbW9kdWxlcy94bWxidWlsZGVyL2xpYi9YTUxOb2RlTGlzdC5qcyJdLCJzb3VyY2VzQ29udGVudCI6WyIvLyBHZW5lcmF0ZWQgYnkgQ29mZmVlU2NyaXB0IDEuMTIuN1xuKGZ1bmN0aW9uKCkge1xuICB2YXIgWE1MTm9kZUxpc3Q7XG5cbiAgbW9kdWxlLmV4cG9ydHMgPSBYTUxOb2RlTGlzdCA9IChmdW5jdGlvbigpIHtcbiAgICBmdW5jdGlvbiBYTUxOb2RlTGlzdChub2Rlcykge1xuICAgICAgdGhpcy5ub2RlcyA9IG5vZGVzO1xuICAgIH1cblxuICAgIE9iamVjdC5kZWZpbmVQcm9wZXJ0eShYTUxOb2RlTGlzdC5wcm90b3R5cGUsICdsZW5ndGgnLCB7XG4gICAgICBnZXQ6IGZ1bmN0aW9uKCkge1xuICAgICAgICByZXR1cm4gdGhpcy5ub2Rlcy5sZW5ndGggfHwgMDtcbiAgICAgIH1cbiAgICB9KTtcblxuICAgIFhNTE5vZGVMaXN0LnByb3RvdHlwZS5jbG9uZSA9IGZ1bmN0aW9uKCkge1xuICAgICAgcmV0dXJuIHRoaXMubm9kZXMgPSBudWxsO1xuICAgIH07XG5cbiAgICBYTUxOb2RlTGlzdC5wcm90b3R5cGUuaXRlbSA9IGZ1bmN0aW9uKGluZGV4KSB7XG4gICAgICByZXR1cm4gdGhpcy5ub2Rlc1tpbmRleF0gfHwgbnVsbDtcbiAgICB9O1xuXG4gICAgcmV0dXJuIFhNTE5vZGVMaXN0O1xuXG4gIH0pKCk7XG5cbn0pLmNhbGwodGhpcyk7XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLNodeList.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLProcessingInstruction.js":
/*!*****************************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLProcessingInstruction.js ***!
  \*****************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLCharacterData, XMLProcessingInstruction,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLCharacterData = __webpack_require__(/*! ./XMLCharacterData */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLCharacterData.js\");\n\n  module.exports = XMLProcessingInstruction = (function(superClass) {\n    extend(XMLProcessingInstruction, superClass);\n\n    function XMLProcessingInstruction(parent, target, value) {\n      XMLProcessingInstruction.__super__.constructor.call(this, parent);\n      if (target == null) {\n        throw new Error(\"Missing instruction target. \" + this.debugInfo());\n      }\n      this.type = NodeType.ProcessingInstruction;\n      this.target = this.stringify.insTarget(target);\n      this.name = this.target;\n      if (value) {\n        this.value = this.stringify.insValue(value);\n      }\n    }\n\n    XMLProcessingInstruction.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLProcessingInstruction.prototype.toString = function(options) {\n      return this.options.writer.processingInstruction(this, this.options.writer.filterOptions(options));\n    };\n\n    XMLProcessingInstruction.prototype.isEqualNode = function(node) {\n      if (!XMLProcessingInstruction.__super__.isEqualNode.apply(this, arguments).isEqualNode(node)) {\n        return false;\n      }\n      if (node.target !== this.target) {\n        return false;\n      }\n      return true;\n    };\n\n    return XMLProcessingInstruction;\n\n  })(XMLCharacterData);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLProcessingInstruction.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLRaw.js":
/*!***********************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLRaw.js ***!
  \***********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLNode, XMLRaw,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLNode = __webpack_require__(/*! ./XMLNode */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLNode.js\");\n\n  module.exports = XMLRaw = (function(superClass) {\n    extend(XMLRaw, superClass);\n\n    function XMLRaw(parent, text) {\n      XMLRaw.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing raw text. \" + this.debugInfo());\n      }\n      this.type = NodeType.Raw;\n      this.value = this.stringify.raw(text);\n    }\n\n    XMLRaw.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLRaw.prototype.toString = function(options) {\n      return this.options.writer.raw(this, this.options.writer.filterOptions(options));\n    };\n\n    return XMLRaw;\n\n  })(XMLNode);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLRaw.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLStreamWriter.js":
/*!********************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLStreamWriter.js ***!
  \********************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, WriterState, XMLStreamWriter, XMLWriterBase,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLWriterBase = __webpack_require__(/*! ./XMLWriterBase */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLWriterBase.js\");\n\n  WriterState = __webpack_require__(/*! ./WriterState */ \"(rsc)/./node_modules/xmlbuilder/lib/WriterState.js\");\n\n  module.exports = XMLStreamWriter = (function(superClass) {\n    extend(XMLStreamWriter, superClass);\n\n    function XMLStreamWriter(stream, options) {\n      this.stream = stream;\n      XMLStreamWriter.__super__.constructor.call(this, options);\n    }\n\n    XMLStreamWriter.prototype.endline = function(node, options, level) {\n      if (node.isLastRootNode && options.state === WriterState.CloseTag) {\n        return '';\n      } else {\n        return XMLStreamWriter.__super__.endline.call(this, node, options, level);\n      }\n    };\n\n    XMLStreamWriter.prototype.document = function(doc, options) {\n      var child, i, j, k, len, len1, ref, ref1, results;\n      ref = doc.children;\n      for (i = j = 0, len = ref.length; j < len; i = ++j) {\n        child = ref[i];\n        child.isLastRootNode = i === doc.children.length - 1;\n      }\n      options = this.filterOptions(options);\n      ref1 = doc.children;\n      results = [];\n      for (k = 0, len1 = ref1.length; k < len1; k++) {\n        child = ref1[k];\n        results.push(this.writeChildNode(child, options, 0));\n      }\n      return results;\n    };\n\n    XMLStreamWriter.prototype.attribute = function(att, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.attribute.call(this, att, options, level));\n    };\n\n    XMLStreamWriter.prototype.cdata = function(node, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.cdata.call(this, node, options, level));\n    };\n\n    XMLStreamWriter.prototype.comment = function(node, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.comment.call(this, node, options, level));\n    };\n\n    XMLStreamWriter.prototype.declaration = function(node, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.declaration.call(this, node, options, level));\n    };\n\n    XMLStreamWriter.prototype.docType = function(node, options, level) {\n      var child, j, len, ref;\n      level || (level = 0);\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      this.stream.write(this.indent(node, options, level));\n      this.stream.write('<!DOCTYPE ' + node.root().name);\n      if (node.pubID && node.sysID) {\n        this.stream.write(' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"');\n      } else if (node.sysID) {\n        this.stream.write(' SYSTEM \"' + node.sysID + '\"');\n      }\n      if (node.children.length > 0) {\n        this.stream.write(' [');\n        this.stream.write(this.endline(node, options, level));\n        options.state = WriterState.InsideTag;\n        ref = node.children;\n        for (j = 0, len = ref.length; j < len; j++) {\n          child = ref[j];\n          this.writeChildNode(child, options, level + 1);\n        }\n        options.state = WriterState.CloseTag;\n        this.stream.write(']');\n      }\n      options.state = WriterState.CloseTag;\n      this.stream.write(options.spaceBeforeSlash + '>');\n      this.stream.write(this.endline(node, options, level));\n      options.state = WriterState.None;\n      return this.closeNode(node, options, level);\n    };\n\n    XMLStreamWriter.prototype.element = function(node, options, level) {\n      var att, child, childNodeCount, firstChildNode, j, len, name, prettySuppressed, ref, ref1;\n      level || (level = 0);\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      this.stream.write(this.indent(node, options, level) + '<' + node.name);\n      ref = node.attribs;\n      for (name in ref) {\n        if (!hasProp.call(ref, name)) continue;\n        att = ref[name];\n        this.attribute(att, options, level);\n      }\n      childNodeCount = node.children.length;\n      firstChildNode = childNodeCount === 0 ? null : node.children[0];\n      if (childNodeCount === 0 || node.children.every(function(e) {\n        return (e.type === NodeType.Text || e.type === NodeType.Raw) && e.value === '';\n      })) {\n        if (options.allowEmpty) {\n          this.stream.write('>');\n          options.state = WriterState.CloseTag;\n          this.stream.write('</' + node.name + '>');\n        } else {\n          options.state = WriterState.CloseTag;\n          this.stream.write(options.spaceBeforeSlash + '/>');\n        }\n      } else if (options.pretty && childNodeCount === 1 && (firstChildNode.type === NodeType.Text || firstChildNode.type === NodeType.Raw) && (firstChildNode.value != null)) {\n        this.stream.write('>');\n        options.state = WriterState.InsideTag;\n        options.suppressPrettyCount++;\n        prettySuppressed = true;\n        this.writeChildNode(firstChildNode, options, level + 1);\n        options.suppressPrettyCount--;\n        prettySuppressed = false;\n        options.state = WriterState.CloseTag;\n        this.stream.write('</' + node.name + '>');\n      } else {\n        this.stream.write('>' + this.endline(node, options, level));\n        options.state = WriterState.InsideTag;\n        ref1 = node.children;\n        for (j = 0, len = ref1.length; j < len; j++) {\n          child = ref1[j];\n          this.writeChildNode(child, options, level + 1);\n        }\n        options.state = WriterState.CloseTag;\n        this.stream.write(this.indent(node, options, level) + '</' + node.name + '>');\n      }\n      this.stream.write(this.endline(node, options, level));\n      options.state = WriterState.None;\n      return this.closeNode(node, options, level);\n    };\n\n    XMLStreamWriter.prototype.processingInstruction = function(node, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.processingInstruction.call(this, node, options, level));\n    };\n\n    XMLStreamWriter.prototype.raw = function(node, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.raw.call(this, node, options, level));\n    };\n\n    XMLStreamWriter.prototype.text = function(node, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.text.call(this, node, options, level));\n    };\n\n    XMLStreamWriter.prototype.dtdAttList = function(node, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.dtdAttList.call(this, node, options, level));\n    };\n\n    XMLStreamWriter.prototype.dtdElement = function(node, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.dtdElement.call(this, node, options, level));\n    };\n\n    XMLStreamWriter.prototype.dtdEntity = function(node, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.dtdEntity.call(this, node, options, level));\n    };\n\n    XMLStreamWriter.prototype.dtdNotation = function(node, options, level) {\n      return this.stream.write(XMLStreamWriter.__super__.dtdNotation.call(this, node, options, level));\n    };\n\n    return XMLStreamWriter;\n\n  })(XMLWriterBase);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLStreamWriter.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLStringWriter.js":
/*!********************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLStringWriter.js ***!
  \********************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLStringWriter, XMLWriterBase,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  XMLWriterBase = __webpack_require__(/*! ./XMLWriterBase */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLWriterBase.js\");\n\n  module.exports = XMLStringWriter = (function(superClass) {\n    extend(XMLStringWriter, superClass);\n\n    function XMLStringWriter(options) {\n      XMLStringWriter.__super__.constructor.call(this, options);\n    }\n\n    XMLStringWriter.prototype.document = function(doc, options) {\n      var child, i, len, r, ref;\n      options = this.filterOptions(options);\n      r = '';\n      ref = doc.children;\n      for (i = 0, len = ref.length; i < len; i++) {\n        child = ref[i];\n        r += this.writeChildNode(child, options, 0);\n      }\n      if (options.pretty && r.slice(-options.newline.length) === options.newline) {\n        r = r.slice(0, -options.newline.length);\n      }\n      return r;\n    };\n\n    return XMLStringWriter;\n\n  })(XMLWriterBase);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLStringWriter.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLStringifier.js":
/*!*******************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLStringifier.js ***!
  \*******************************************************/
/***/ (function(module) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var XMLStringifier,\n    bind = function(fn, me){ return function(){ return fn.apply(me, arguments); }; },\n    hasProp = {}.hasOwnProperty;\n\n  module.exports = XMLStringifier = (function() {\n    function XMLStringifier(options) {\n      this.assertLegalName = bind(this.assertLegalName, this);\n      this.assertLegalChar = bind(this.assertLegalChar, this);\n      var key, ref, value;\n      options || (options = {});\n      this.options = options;\n      if (!this.options.version) {\n        this.options.version = '1.0';\n      }\n      ref = options.stringify || {};\n      for (key in ref) {\n        if (!hasProp.call(ref, key)) continue;\n        value = ref[key];\n        this[key] = value;\n      }\n    }\n\n    XMLStringifier.prototype.name = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalName('' + val || '');\n    };\n\n    XMLStringifier.prototype.text = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalChar(this.textEscape('' + val || ''));\n    };\n\n    XMLStringifier.prototype.cdata = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      val = '' + val || '';\n      val = val.replace(']]>', ']]]]><![CDATA[>');\n      return this.assertLegalChar(val);\n    };\n\n    XMLStringifier.prototype.comment = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      val = '' + val || '';\n      if (val.match(/--/)) {\n        throw new Error(\"Comment text cannot contain double-hypen: \" + val);\n      }\n      return this.assertLegalChar(val);\n    };\n\n    XMLStringifier.prototype.raw = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return '' + val || '';\n    };\n\n    XMLStringifier.prototype.attValue = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalChar(this.attEscape(val = '' + val || ''));\n    };\n\n    XMLStringifier.prototype.insTarget = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalChar('' + val || '');\n    };\n\n    XMLStringifier.prototype.insValue = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      val = '' + val || '';\n      if (val.match(/\\?>/)) {\n        throw new Error(\"Invalid processing instruction value: \" + val);\n      }\n      return this.assertLegalChar(val);\n    };\n\n    XMLStringifier.prototype.xmlVersion = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      val = '' + val || '';\n      if (!val.match(/1\\.[0-9]+/)) {\n        throw new Error(\"Invalid version number: \" + val);\n      }\n      return val;\n    };\n\n    XMLStringifier.prototype.xmlEncoding = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      val = '' + val || '';\n      if (!val.match(/^[A-Za-z](?:[A-Za-z0-9._-])*$/)) {\n        throw new Error(\"Invalid encoding: \" + val);\n      }\n      return this.assertLegalChar(val);\n    };\n\n    XMLStringifier.prototype.xmlStandalone = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      if (val) {\n        return \"yes\";\n      } else {\n        return \"no\";\n      }\n    };\n\n    XMLStringifier.prototype.dtdPubID = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalChar('' + val || '');\n    };\n\n    XMLStringifier.prototype.dtdSysID = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalChar('' + val || '');\n    };\n\n    XMLStringifier.prototype.dtdElementValue = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalChar('' + val || '');\n    };\n\n    XMLStringifier.prototype.dtdAttType = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalChar('' + val || '');\n    };\n\n    XMLStringifier.prototype.dtdAttDefault = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalChar('' + val || '');\n    };\n\n    XMLStringifier.prototype.dtdEntityValue = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalChar('' + val || '');\n    };\n\n    XMLStringifier.prototype.dtdNData = function(val) {\n      if (this.options.noValidation) {\n        return val;\n      }\n      return this.assertLegalChar('' + val || '');\n    };\n\n    XMLStringifier.prototype.convertAttKey = '@';\n\n    XMLStringifier.prototype.convertPIKey = '?';\n\n    XMLStringifier.prototype.convertTextKey = '#text';\n\n    XMLStringifier.prototype.convertCDataKey = '#cdata';\n\n    XMLStringifier.prototype.convertCommentKey = '#comment';\n\n    XMLStringifier.prototype.convertRawKey = '#raw';\n\n    XMLStringifier.prototype.assertLegalChar = function(str) {\n      var regex, res;\n      if (this.options.noValidation) {\n        return str;\n      }\n      regex = '';\n      if (this.options.version === '1.0') {\n        regex = /[\\0-\\x08\\x0B\\f\\x0E-\\x1F\\uFFFE\\uFFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF]/;\n        if (res = str.match(regex)) {\n          throw new Error(\"Invalid character in string: \" + str + \" at index \" + res.index);\n        }\n      } else if (this.options.version === '1.1') {\n        regex = /[\\0\\uFFFE\\uFFFF]|[\\uD800-\\uDBFF](?![\\uDC00-\\uDFFF])|(?:[^\\uD800-\\uDBFF]|^)[\\uDC00-\\uDFFF]/;\n        if (res = str.match(regex)) {\n          throw new Error(\"Invalid character in string: \" + str + \" at index \" + res.index);\n        }\n      }\n      return str;\n    };\n\n    XMLStringifier.prototype.assertLegalName = function(str) {\n      var regex;\n      if (this.options.noValidation) {\n        return str;\n      }\n      this.assertLegalChar(str);\n      regex = /^([:A-Z_a-z\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u02FF\\u0370-\\u037D\\u037F-\\u1FFF\\u200C\\u200D\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]|[\\uD800-\\uDB7F][\\uDC00-\\uDFFF])([\\x2D\\.0-:A-Z_a-z\\xB7\\xC0-\\xD6\\xD8-\\xF6\\xF8-\\u037D\\u037F-\\u1FFF\\u200C\\u200D\\u203F\\u2040\\u2070-\\u218F\\u2C00-\\u2FEF\\u3001-\\uD7FF\\uF900-\\uFDCF\\uFDF0-\\uFFFD]|[\\uD800-\\uDB7F][\\uDC00-\\uDFFF])*$/;\n      if (!str.match(regex)) {\n        throw new Error(\"Invalid character in name\");\n      }\n      return str;\n    };\n\n    XMLStringifier.prototype.textEscape = function(str) {\n      var ampregex;\n      if (this.options.noValidation) {\n        return str;\n      }\n      ampregex = this.options.noDoubleEncoding ? /(?!&\\S+;)&/g : /&/g;\n      return str.replace(ampregex, '&amp;').replace(/</g, '&lt;').replace(/>/g, '&gt;').replace(/\\r/g, '&#xD;');\n    };\n\n    XMLStringifier.prototype.attEscape = function(str) {\n      var ampregex;\n      if (this.options.noValidation) {\n        return str;\n      }\n      ampregex = this.options.noDoubleEncoding ? /(?!&\\S+;)&/g : /&/g;\n      return str.replace(ampregex, '&amp;').replace(/</g, '&lt;').replace(/\"/g, '&quot;').replace(/\\t/g, '&#x9;').replace(/\\n/g, '&#xA;').replace(/\\r/g, '&#xD;');\n    };\n\n    return XMLStringifier;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLStringifier.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLText.js":
/*!************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLText.js ***!
  \************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, XMLCharacterData, XMLText,\n    extend = function(child, parent) { for (var key in parent) { if (hasProp.call(parent, key)) child[key] = parent[key]; } function ctor() { this.constructor = child; } ctor.prototype = parent.prototype; child.prototype = new ctor(); child.__super__ = parent.prototype; return child; },\n    hasProp = {}.hasOwnProperty;\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLCharacterData = __webpack_require__(/*! ./XMLCharacterData */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLCharacterData.js\");\n\n  module.exports = XMLText = (function(superClass) {\n    extend(XMLText, superClass);\n\n    function XMLText(parent, text) {\n      XMLText.__super__.constructor.call(this, parent);\n      if (text == null) {\n        throw new Error(\"Missing element text. \" + this.debugInfo());\n      }\n      this.name = \"#text\";\n      this.type = NodeType.Text;\n      this.value = this.stringify.text(text);\n    }\n\n    Object.defineProperty(XMLText.prototype, 'isElementContentWhitespace', {\n      get: function() {\n        throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n      }\n    });\n\n    Object.defineProperty(XMLText.prototype, 'wholeText', {\n      get: function() {\n        var next, prev, str;\n        str = '';\n        prev = this.previousSibling;\n        while (prev) {\n          str = prev.data + str;\n          prev = prev.previousSibling;\n        }\n        str += this.data;\n        next = this.nextSibling;\n        while (next) {\n          str = str + next.data;\n          next = next.nextSibling;\n        }\n        return str;\n      }\n    });\n\n    XMLText.prototype.clone = function() {\n      return Object.create(this);\n    };\n\n    XMLText.prototype.toString = function(options) {\n      return this.options.writer.text(this, this.options.writer.filterOptions(options));\n    };\n\n    XMLText.prototype.splitText = function(offset) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    XMLText.prototype.replaceWholeText = function(content) {\n      throw new Error(\"This DOM method is not implemented.\" + this.debugInfo());\n    };\n\n    return XMLText;\n\n  })(XMLCharacterData);\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLText.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/XMLWriterBase.js":
/*!******************************************************!*\
  !*** ./node_modules/xmlbuilder/lib/XMLWriterBase.js ***!
  \******************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, WriterState, XMLCData, XMLComment, XMLDTDAttList, XMLDTDElement, XMLDTDEntity, XMLDTDNotation, XMLDeclaration, XMLDocType, XMLDummy, XMLElement, XMLProcessingInstruction, XMLRaw, XMLText, XMLWriterBase, assign,\n    hasProp = {}.hasOwnProperty;\n\n  assign = (__webpack_require__(/*! ./Utility */ \"(rsc)/./node_modules/xmlbuilder/lib/Utility.js\").assign);\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  XMLDeclaration = __webpack_require__(/*! ./XMLDeclaration */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDeclaration.js\");\n\n  XMLDocType = __webpack_require__(/*! ./XMLDocType */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDocType.js\");\n\n  XMLCData = __webpack_require__(/*! ./XMLCData */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLCData.js\");\n\n  XMLComment = __webpack_require__(/*! ./XMLComment */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLComment.js\");\n\n  XMLElement = __webpack_require__(/*! ./XMLElement */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLElement.js\");\n\n  XMLRaw = __webpack_require__(/*! ./XMLRaw */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLRaw.js\");\n\n  XMLText = __webpack_require__(/*! ./XMLText */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLText.js\");\n\n  XMLProcessingInstruction = __webpack_require__(/*! ./XMLProcessingInstruction */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLProcessingInstruction.js\");\n\n  XMLDummy = __webpack_require__(/*! ./XMLDummy */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDummy.js\");\n\n  XMLDTDAttList = __webpack_require__(/*! ./XMLDTDAttList */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDTDAttList.js\");\n\n  XMLDTDElement = __webpack_require__(/*! ./XMLDTDElement */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDTDElement.js\");\n\n  XMLDTDEntity = __webpack_require__(/*! ./XMLDTDEntity */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDTDEntity.js\");\n\n  XMLDTDNotation = __webpack_require__(/*! ./XMLDTDNotation */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDTDNotation.js\");\n\n  WriterState = __webpack_require__(/*! ./WriterState */ \"(rsc)/./node_modules/xmlbuilder/lib/WriterState.js\");\n\n  module.exports = XMLWriterBase = (function() {\n    function XMLWriterBase(options) {\n      var key, ref, value;\n      options || (options = {});\n      this.options = options;\n      ref = options.writer || {};\n      for (key in ref) {\n        if (!hasProp.call(ref, key)) continue;\n        value = ref[key];\n        this[\"_\" + key] = this[key];\n        this[key] = value;\n      }\n    }\n\n    XMLWriterBase.prototype.filterOptions = function(options) {\n      var filteredOptions, ref, ref1, ref2, ref3, ref4, ref5, ref6;\n      options || (options = {});\n      options = assign({}, this.options, options);\n      filteredOptions = {\n        writer: this\n      };\n      filteredOptions.pretty = options.pretty || false;\n      filteredOptions.allowEmpty = options.allowEmpty || false;\n      filteredOptions.indent = (ref = options.indent) != null ? ref : '  ';\n      filteredOptions.newline = (ref1 = options.newline) != null ? ref1 : '\\n';\n      filteredOptions.offset = (ref2 = options.offset) != null ? ref2 : 0;\n      filteredOptions.dontPrettyTextNodes = (ref3 = (ref4 = options.dontPrettyTextNodes) != null ? ref4 : options.dontprettytextnodes) != null ? ref3 : 0;\n      filteredOptions.spaceBeforeSlash = (ref5 = (ref6 = options.spaceBeforeSlash) != null ? ref6 : options.spacebeforeslash) != null ? ref5 : '';\n      if (filteredOptions.spaceBeforeSlash === true) {\n        filteredOptions.spaceBeforeSlash = ' ';\n      }\n      filteredOptions.suppressPrettyCount = 0;\n      filteredOptions.user = {};\n      filteredOptions.state = WriterState.None;\n      return filteredOptions;\n    };\n\n    XMLWriterBase.prototype.indent = function(node, options, level) {\n      var indentLevel;\n      if (!options.pretty || options.suppressPrettyCount) {\n        return '';\n      } else if (options.pretty) {\n        indentLevel = (level || 0) + options.offset + 1;\n        if (indentLevel > 0) {\n          return new Array(indentLevel).join(options.indent);\n        }\n      }\n      return '';\n    };\n\n    XMLWriterBase.prototype.endline = function(node, options, level) {\n      if (!options.pretty || options.suppressPrettyCount) {\n        return '';\n      } else {\n        return options.newline;\n      }\n    };\n\n    XMLWriterBase.prototype.attribute = function(att, options, level) {\n      var r;\n      this.openAttribute(att, options, level);\n      r = ' ' + att.name + '=\"' + att.value + '\"';\n      this.closeAttribute(att, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.cdata = function(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<![CDATA[';\n      options.state = WriterState.InsideTag;\n      r += node.value;\n      options.state = WriterState.CloseTag;\n      r += ']]>' + this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.comment = function(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<!-- ';\n      options.state = WriterState.InsideTag;\n      r += node.value;\n      options.state = WriterState.CloseTag;\n      r += ' -->' + this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.declaration = function(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<?xml';\n      options.state = WriterState.InsideTag;\n      r += ' version=\"' + node.version + '\"';\n      if (node.encoding != null) {\n        r += ' encoding=\"' + node.encoding + '\"';\n      }\n      if (node.standalone != null) {\n        r += ' standalone=\"' + node.standalone + '\"';\n      }\n      options.state = WriterState.CloseTag;\n      r += options.spaceBeforeSlash + '?>';\n      r += this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.docType = function(node, options, level) {\n      var child, i, len, r, ref;\n      level || (level = 0);\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level);\n      r += '<!DOCTYPE ' + node.root().name;\n      if (node.pubID && node.sysID) {\n        r += ' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"';\n      } else if (node.sysID) {\n        r += ' SYSTEM \"' + node.sysID + '\"';\n      }\n      if (node.children.length > 0) {\n        r += ' [';\n        r += this.endline(node, options, level);\n        options.state = WriterState.InsideTag;\n        ref = node.children;\n        for (i = 0, len = ref.length; i < len; i++) {\n          child = ref[i];\n          r += this.writeChildNode(child, options, level + 1);\n        }\n        options.state = WriterState.CloseTag;\n        r += ']';\n      }\n      options.state = WriterState.CloseTag;\n      r += options.spaceBeforeSlash + '>';\n      r += this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.element = function(node, options, level) {\n      var att, child, childNodeCount, firstChildNode, i, j, len, len1, name, prettySuppressed, r, ref, ref1, ref2;\n      level || (level = 0);\n      prettySuppressed = false;\n      r = '';\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r += this.indent(node, options, level) + '<' + node.name;\n      ref = node.attribs;\n      for (name in ref) {\n        if (!hasProp.call(ref, name)) continue;\n        att = ref[name];\n        r += this.attribute(att, options, level);\n      }\n      childNodeCount = node.children.length;\n      firstChildNode = childNodeCount === 0 ? null : node.children[0];\n      if (childNodeCount === 0 || node.children.every(function(e) {\n        return (e.type === NodeType.Text || e.type === NodeType.Raw) && e.value === '';\n      })) {\n        if (options.allowEmpty) {\n          r += '>';\n          options.state = WriterState.CloseTag;\n          r += '</' + node.name + '>' + this.endline(node, options, level);\n        } else {\n          options.state = WriterState.CloseTag;\n          r += options.spaceBeforeSlash + '/>' + this.endline(node, options, level);\n        }\n      } else if (options.pretty && childNodeCount === 1 && (firstChildNode.type === NodeType.Text || firstChildNode.type === NodeType.Raw) && (firstChildNode.value != null)) {\n        r += '>';\n        options.state = WriterState.InsideTag;\n        options.suppressPrettyCount++;\n        prettySuppressed = true;\n        r += this.writeChildNode(firstChildNode, options, level + 1);\n        options.suppressPrettyCount--;\n        prettySuppressed = false;\n        options.state = WriterState.CloseTag;\n        r += '</' + node.name + '>' + this.endline(node, options, level);\n      } else {\n        if (options.dontPrettyTextNodes) {\n          ref1 = node.children;\n          for (i = 0, len = ref1.length; i < len; i++) {\n            child = ref1[i];\n            if ((child.type === NodeType.Text || child.type === NodeType.Raw) && (child.value != null)) {\n              options.suppressPrettyCount++;\n              prettySuppressed = true;\n              break;\n            }\n          }\n        }\n        r += '>' + this.endline(node, options, level);\n        options.state = WriterState.InsideTag;\n        ref2 = node.children;\n        for (j = 0, len1 = ref2.length; j < len1; j++) {\n          child = ref2[j];\n          r += this.writeChildNode(child, options, level + 1);\n        }\n        options.state = WriterState.CloseTag;\n        r += this.indent(node, options, level) + '</' + node.name + '>';\n        if (prettySuppressed) {\n          options.suppressPrettyCount--;\n        }\n        r += this.endline(node, options, level);\n        options.state = WriterState.None;\n      }\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.writeChildNode = function(node, options, level) {\n      switch (node.type) {\n        case NodeType.CData:\n          return this.cdata(node, options, level);\n        case NodeType.Comment:\n          return this.comment(node, options, level);\n        case NodeType.Element:\n          return this.element(node, options, level);\n        case NodeType.Raw:\n          return this.raw(node, options, level);\n        case NodeType.Text:\n          return this.text(node, options, level);\n        case NodeType.ProcessingInstruction:\n          return this.processingInstruction(node, options, level);\n        case NodeType.Dummy:\n          return '';\n        case NodeType.Declaration:\n          return this.declaration(node, options, level);\n        case NodeType.DocType:\n          return this.docType(node, options, level);\n        case NodeType.AttributeDeclaration:\n          return this.dtdAttList(node, options, level);\n        case NodeType.ElementDeclaration:\n          return this.dtdElement(node, options, level);\n        case NodeType.EntityDeclaration:\n          return this.dtdEntity(node, options, level);\n        case NodeType.NotationDeclaration:\n          return this.dtdNotation(node, options, level);\n        default:\n          throw new Error(\"Unknown XML node type: \" + node.constructor.name);\n      }\n    };\n\n    XMLWriterBase.prototype.processingInstruction = function(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<?';\n      options.state = WriterState.InsideTag;\n      r += node.target;\n      if (node.value) {\n        r += ' ' + node.value;\n      }\n      options.state = WriterState.CloseTag;\n      r += options.spaceBeforeSlash + '?>';\n      r += this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.raw = function(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level);\n      options.state = WriterState.InsideTag;\n      r += node.value;\n      options.state = WriterState.CloseTag;\n      r += this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.text = function(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level);\n      options.state = WriterState.InsideTag;\n      r += node.value;\n      options.state = WriterState.CloseTag;\n      r += this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.dtdAttList = function(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<!ATTLIST';\n      options.state = WriterState.InsideTag;\n      r += ' ' + node.elementName + ' ' + node.attributeName + ' ' + node.attributeType;\n      if (node.defaultValueType !== '#DEFAULT') {\n        r += ' ' + node.defaultValueType;\n      }\n      if (node.defaultValue) {\n        r += ' \"' + node.defaultValue + '\"';\n      }\n      options.state = WriterState.CloseTag;\n      r += options.spaceBeforeSlash + '>' + this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.dtdElement = function(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<!ELEMENT';\n      options.state = WriterState.InsideTag;\n      r += ' ' + node.name + ' ' + node.value;\n      options.state = WriterState.CloseTag;\n      r += options.spaceBeforeSlash + '>' + this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.dtdEntity = function(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<!ENTITY';\n      options.state = WriterState.InsideTag;\n      if (node.pe) {\n        r += ' %';\n      }\n      r += ' ' + node.name;\n      if (node.value) {\n        r += ' \"' + node.value + '\"';\n      } else {\n        if (node.pubID && node.sysID) {\n          r += ' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"';\n        } else if (node.sysID) {\n          r += ' SYSTEM \"' + node.sysID + '\"';\n        }\n        if (node.nData) {\n          r += ' NDATA ' + node.nData;\n        }\n      }\n      options.state = WriterState.CloseTag;\n      r += options.spaceBeforeSlash + '>' + this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.dtdNotation = function(node, options, level) {\n      var r;\n      this.openNode(node, options, level);\n      options.state = WriterState.OpenTag;\n      r = this.indent(node, options, level) + '<!NOTATION';\n      options.state = WriterState.InsideTag;\n      r += ' ' + node.name;\n      if (node.pubID && node.sysID) {\n        r += ' PUBLIC \"' + node.pubID + '\" \"' + node.sysID + '\"';\n      } else if (node.pubID) {\n        r += ' PUBLIC \"' + node.pubID + '\"';\n      } else if (node.sysID) {\n        r += ' SYSTEM \"' + node.sysID + '\"';\n      }\n      options.state = WriterState.CloseTag;\n      r += options.spaceBeforeSlash + '>' + this.endline(node, options, level);\n      options.state = WriterState.None;\n      this.closeNode(node, options, level);\n      return r;\n    };\n\n    XMLWriterBase.prototype.openNode = function(node, options, level) {};\n\n    XMLWriterBase.prototype.closeNode = function(node, options, level) {};\n\n    XMLWriterBase.prototype.openAttribute = function(att, options, level) {};\n\n    XMLWriterBase.prototype.closeAttribute = function(att, options, level) {};\n\n    return XMLWriterBase;\n\n  })();\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/XMLWriterBase.js\n");

/***/ }),

/***/ "(rsc)/./node_modules/xmlbuilder/lib/index.js":
/*!**********************************************!*\
  !*** ./node_modules/xmlbuilder/lib/index.js ***!
  \**********************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval("// Generated by CoffeeScript 1.12.7\n(function() {\n  var NodeType, WriterState, XMLDOMImplementation, XMLDocument, XMLDocumentCB, XMLStreamWriter, XMLStringWriter, assign, isFunction, ref;\n\n  ref = __webpack_require__(/*! ./Utility */ \"(rsc)/./node_modules/xmlbuilder/lib/Utility.js\"), assign = ref.assign, isFunction = ref.isFunction;\n\n  XMLDOMImplementation = __webpack_require__(/*! ./XMLDOMImplementation */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDOMImplementation.js\");\n\n  XMLDocument = __webpack_require__(/*! ./XMLDocument */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDocument.js\");\n\n  XMLDocumentCB = __webpack_require__(/*! ./XMLDocumentCB */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLDocumentCB.js\");\n\n  XMLStringWriter = __webpack_require__(/*! ./XMLStringWriter */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLStringWriter.js\");\n\n  XMLStreamWriter = __webpack_require__(/*! ./XMLStreamWriter */ \"(rsc)/./node_modules/xmlbuilder/lib/XMLStreamWriter.js\");\n\n  NodeType = __webpack_require__(/*! ./NodeType */ \"(rsc)/./node_modules/xmlbuilder/lib/NodeType.js\");\n\n  WriterState = __webpack_require__(/*! ./WriterState */ \"(rsc)/./node_modules/xmlbuilder/lib/WriterState.js\");\n\n  module.exports.create = function(name, xmldec, doctype, options) {\n    var doc, root;\n    if (name == null) {\n      throw new Error(\"Root element needs a name.\");\n    }\n    options = assign({}, xmldec, doctype, options);\n    doc = new XMLDocument(options);\n    root = doc.element(name);\n    if (!options.headless) {\n      doc.declaration(options);\n      if ((options.pubID != null) || (options.sysID != null)) {\n        doc.dtd(options);\n      }\n    }\n    return root;\n  };\n\n  module.exports.begin = function(options, onData, onEnd) {\n    var ref1;\n    if (isFunction(options)) {\n      ref1 = [options, onData], onData = ref1[0], onEnd = ref1[1];\n      options = {};\n    }\n    if (onData) {\n      return new XMLDocumentCB(options, onData, onEnd);\n    } else {\n      return new XMLDocument(options);\n    }\n  };\n\n  module.exports.stringWriter = function(options) {\n    return new XMLStringWriter(options);\n  };\n\n  module.exports.streamWriter = function(stream, options) {\n    return new XMLStreamWriter(stream, options);\n  };\n\n  module.exports.implementation = new XMLDOMImplementation();\n\n  module.exports.nodeType = NodeType;\n\n  module.exports.writerState = WriterState;\n\n}).call(this);\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/xmlbuilder/lib/index.js\n");

/***/ })

};
;